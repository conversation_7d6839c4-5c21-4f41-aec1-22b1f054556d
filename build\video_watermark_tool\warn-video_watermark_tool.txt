
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named pep517 - imported by importlib.metadata (delayed)
missing module named 'org.python' - imported by copy (optional), xml.sax (delayed, conditional)
missing module named grp - imported by shutil (optional), tarfile (optional), pathlib (delayed, optional), subprocess (optional), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.archive_util (optional)
missing module named pwd - imported by posixpath (delayed, conditional), shutil (optional), tarfile (optional), pathlib (delayed, conditional, optional), subprocess (optional), http.server (delayed, optional), webbrowser (delayed), psutil (optional), netrc (delayed, conditional), getpass (delayed), distutils.util (delayed, conditional, optional), distutils.archive_util (optional), setuptools._vendor.backports.tarfile (optional), setuptools._distutils.util (delayed, conditional, optional), setuptools._distutils.archive_util (optional)
missing module named posix - imported by os (conditional, optional), shutil (conditional), importlib._bootstrap_external (conditional)
missing module named resource - imported by posix (top-level), fsspec.asyn (conditional, optional)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional), zipimport (top-level)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional), zipimport (top-level)
missing module named _posixsubprocess - imported by subprocess (optional), multiprocessing.util (delayed)
missing module named vms_lib - imported by platform (delayed, optional)
missing module named 'java.lang' - imported by platform (delayed, optional), xml.sax._exceptions (conditional)
missing module named java - imported by platform (delayed)
missing module named _winreg - imported by platform (delayed, optional), moviepy.config (conditional, optional), pygments.formatters.img (optional)
missing module named org - imported by pickle (optional)
missing module named 'distutils._modified' - imported by setuptools._distutils.file_util (delayed)
missing module named '_typeshed.importlib' - imported by pkg_resources (conditional)
missing module named _typeshed - imported by setuptools.command.bdist_wheel (conditional), pkg_resources (conditional), pydantic._internal._dataclasses (conditional), anyio.abc._eventloop (conditional), anyio._core._sockets (conditional), anyio._core._fileio (conditional), httpx._transports.wsgi (conditional), huggingface_hub.hub_mixin (conditional), anyio._backends._asyncio (conditional), anyio._core._asyncio_selector_thread (conditional), anyio._backends._trio (conditional), jaraco.collections (conditional)
missing module named jnius - imported by platformdirs.android (delayed, conditional, optional)
missing module named android - imported by platformdirs.android (delayed, conditional, optional)
missing module named _scproxy - imported by urllib.request (conditional), future.backports.urllib.request (conditional)
missing module named termios - imported by tty (top-level), getpass (optional), tqdm.utils (delayed, optional), absl.flags._helpers (optional), click._termui_impl (conditional), accelerate.commands.menu.keymap (delayed, conditional), werkzeug._reloader (delayed, optional)
missing module named 'typing.io' - imported by importlib.resources (top-level)
missing module named jaraco.text.yield_lines - imported by setuptools._vendor.jaraco.text (top-level), setuptools._entry_points (top-level), setuptools.command._requirestxt (top-level)
missing module named _manylinux - imported by packaging._manylinux (delayed, optional), setuptools._vendor.packaging._manylinux (delayed, optional), wheel.vendored.packaging._manylinux (delayed, optional)
missing module named 'distutils._log' - imported by setuptools._distutils.command.bdist_dumb (top-level), setuptools._distutils.command.bdist_rpm (top-level), setuptools._distutils.command.build_clib (top-level), setuptools._distutils.command.build_ext (top-level), setuptools._distutils.command.build_py (top-level), setuptools._distutils.command.build_scripts (top-level), setuptools._distutils.command.clean (top-level), setuptools._distutils.command.config (top-level), setuptools._distutils.command.install (top-level), setuptools._distutils.command.install_scripts (top-level), setuptools._distutils.command.sdist (top-level)
missing module named usercustomize - imported by site (delayed, optional)
missing module named sitecustomize - imported by site (delayed, optional)
missing module named readline - imported by cmd (delayed, conditional, optional), code (delayed, conditional, optional), pdb (delayed, optional), site (delayed, optional), rlcompleter (optional), pstats (conditional, optional), sympy.interactive.session (delayed, optional), websockets.__main__ (optional)
missing module named 'unittest.mock' - imported by torch._dynamo.eval_frame (top-level), torch._guards (top-level), torch._dispatch.python (top-level), torch._dynamo.testing (top-level), torch._functorch.aot_autograd (top-level), torch._dynamo.symbolic_convert (top-level), torch.utils.data.datapipes.dataframe.dataframes (delayed), torch._dynamo.backends.common (top-level), torch._inductor.debug (top-level), torch._inductor.ir (top-level), torch._inductor.virtualized (top-level), torch._export (top-level), torch.testing._internal.common_utils (top-level), torch._dynamo.test_minifier_common (top-level), torch._inductor.select_algorithm (top-level), torch.testing._internal.common_distributed (top-level), torch.testing._internal.logging_utils (top-level), setuptools._distutils._msvccompiler (top-level)
missing module named trove_classifiers - imported by setuptools.config._validate_pyproject.formats (optional)
missing module named tomllib - imported by setuptools.compat.py310 (conditional)
missing module named pyimod02_importers - imported by F:\Program Files\anaconda\envs\watermark\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgutil.py (delayed), F:\Program Files\anaconda\envs\watermark\Lib\site-packages\PyInstaller\hooks\rthooks\pyi_rth_pkgres.py (delayed)
missing module named _posixshmem - imported by multiprocessing.resource_tracker (conditional), multiprocessing.shared_memory (conditional)
missing module named multiprocessing.BufferTooShort - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.AuthenticationError - imported by multiprocessing (top-level), multiprocessing.connection (top-level)
missing module named multiprocessing.get_context - imported by multiprocessing (top-level), multiprocessing.pool (top-level), multiprocessing.managers (top-level), multiprocessing.sharedctypes (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named multiprocessing.TimeoutError - imported by multiprocessing (top-level), multiprocessing.pool (top-level)
missing module named multiprocessing.set_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level)
missing module named multiprocessing.get_start_method - imported by multiprocessing (top-level), multiprocessing.spawn (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level)
missing module named multiprocessing.Queue - imported by multiprocessing (top-level), transformers.benchmark.benchmark_utils (top-level)
missing module named multiprocessing.Process - imported by multiprocessing (top-level), transformers.benchmark.benchmark_utils (top-level)
missing module named multiprocessing.cpu_count - imported by multiprocessing (top-level), transformers.data.processors.squad (top-level), skimage.util.apply_parallel (delayed, conditional, optional)
missing module named multiprocessing.Value - imported by multiprocessing (top-level), timm.data.readers.shared_count (top-level)
missing module named multiprocessing.Pool - imported by multiprocessing (top-level), torchvision.datasets.kinetics (top-level), transformers.models.nougat.tokenization_nougat_fast (top-level), transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (top-level), transformers.data.processors.squad (top-level)
missing module named multiprocessing.Pipe - imported by multiprocessing (top-level), uvicorn.supervisors.multiprocess (top-level), transformers.benchmark.benchmark_utils (top-level)
missing module named multiprocessing.RLock - imported by multiprocessing (delayed, conditional, optional), tqdm.std (delayed, conditional, optional)
missing module named 'IPython.display' - imported by tqdm.notebook (conditional, optional), moviepy.video.io.html_tools (optional), rich.jupyter (delayed, optional), rich.live (delayed, conditional, optional), huggingface_hub._login (delayed, optional), gradio.blocks (delayed, conditional, optional), altair.vegalite.v5.display (delayed), altair.vegalite.v5.api (delayed, conditional), transformers.utils.notebook (top-level), transformers.agents.agent_types (delayed)
missing module named 'IPython.html' - imported by tqdm.notebook (conditional, optional)
missing module named ipywidgets - imported by tqdm.notebook (conditional, optional), rich.live (delayed, conditional, optional)
missing module named setuptools_scm - imported by tqdm.version (optional)
excluded module named pandas - imported by tqdm.std (delayed, optional), networkx.convert (delayed, optional), networkx.convert_matrix (delayed), networkx.algorithms.centrality.group (delayed), narwhals.dependencies (conditional), narwhals.utils (delayed, conditional), narwhals.series (conditional), narwhals._polars.dataframe (delayed, conditional), narwhals._duckdb.dataframe (delayed, conditional), narwhals._arrow.dataframe (delayed, conditional), narwhals._dask.dataframe (top-level), narwhals._pandas_like.utils (top-level), narwhals._pandas_like.series (conditional), narwhals._pandas_like.namespace (conditional), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._dask.namespace (top-level), narwhals._compliant.series (conditional), narwhals._dask.group_by (conditional), narwhals._ibis.dataframe (conditional), narwhals._spark_like.dataframe (delayed, conditional), narwhals._interchange.dataframe (delayed, conditional), narwhals.schema (delayed), narwhals.dataframe (conditional), narwhals._compliant.dataframe (conditional), narwhals._arrow.series (delayed, conditional), altair.utils.data (delayed, conditional, optional), altair.utils.core (delayed, conditional), gradio.components.bar_plot (top-level), gradio.components.dataframe (top-level), gradio.components.line_plot (top-level), gradio.components.scatter_plot (top-level), torch.utils.data.datapipes.dataframe.dataframe_wrapper (delayed, optional), transformers.models.tapas.tokenization_tapas (conditional), transformers.integrations.integration_utils (delayed, conditional), transformers.pipelines.table_question_answering (delayed), transformers.agents.python_interpreter (conditional), transformers.models.deprecated.tapex.tokenization_tapex (conditional)
missing module named 'pandas.core' - imported by tqdm.std (delayed, optional), narwhals._dask.group_by (conditional)
missing module named fcntl - imported by tqdm.utils (delayed, optional), filelock._unix (conditional, optional), pty (delayed, optional), absl.flags._helpers (optional), torch.testing._internal.distributed.distributed_test (conditional)
missing module named 'matplotlib.pyplot' - imported by tqdm.gui (delayed), moviepy.video.io.sliders (top-level), networkx.drawing.nx_pylab (delayed), torch.utils.tensorboard._utils (delayed), gradio.helpers (delayed), gradio.utils (delayed), torch.profiler._memory_profiler (delayed), moviepy.video.tools.segmenting (delayed, conditional), skimage.io._plugins.matplotlib_plugin (delayed), mpl_toolkits.axes_grid1.parasite_axes (delayed), torch.ao.quantization.fx._model_report.model_report_visualizer (optional), torch.distributed._tools.memory_tracker (delayed)
excluded module named matplotlib - imported by tqdm.gui (delayed), mpl_toolkits.mplot3d.axes3d (top-level), mpl_toolkits.mplot3d.art3d (top-level), mpl_toolkits.mplot3d.proj3d (top-level), mpl_toolkits.mplot3d.axis3d (top-level), sympy.testing.runtests (delayed, conditional), networkx.drawing.nx_pylab (delayed), gradio.utils (delayed), imageio.plugins._tifffile (delayed, conditional, optional), tifffile.tifffile (delayed, conditional, optional), mpl_toolkits.axes_grid1.axes_size (top-level), mpl_toolkits.axes_grid1.axes_divider (top-level), mpl_toolkits.axes_grid1.axes_grid (top-level), mpl_toolkits.axes_grid1.parasite_axes (top-level)
missing module named 'win32com.gen_py' - imported by win32com (conditional, optional)
missing module named tensorboard_plugin_wit - imported by tensorboard.plugins.wit_redirect.wit_redirect_plugin (delayed, optional)
missing module named pygments.formatters.Terminal256Formatter - imported by pygments.formatters (conditional), transformers.agents.agents (conditional)
missing module named pygments.lexers.PrologLexer - imported by pygments.lexers (top-level), pygments.lexers.cplint (top-level)
missing module named pygments.lexers.PythonLexer - imported by pygments.lexers (conditional), transformers.agents.agents (conditional)
missing module named ctags - imported by pygments.formatters.html (optional)
missing module named chardet - imported by requests (optional), pygments.lexer (delayed, conditional, optional)
missing module named markdown.blockparsers - imported by markdown.extensions.abbr (conditional)
missing module named StringIO - imported by pydub.audio_segment (optional), six (conditional)
missing module named 'lxml.etree' - imported by networkx.readwrite.graphml (delayed, optional), tensorboard._vendor.html5lib.treebuilders.etree_lxml (top-level), imageio.plugins._tifffile (delayed, optional)
missing module named lxml - imported by sympy.utilities.mathml (delayed), tensorboard._vendor.html5lib.treewalkers.etree_lxml (top-level), tifffile.tifffile (delayed, optional)
missing module named 'genshi.core' - imported by tensorboard._vendor.html5lib.treewalkers.genshi (top-level)
missing module named genshi - imported by tensorboard._vendor.html5lib.treewalkers.genshi (top-level)
runtime module named six.moves - imported by tensorboard._vendor.html5lib._inputstream (top-level), six.moves.urllib (top-level), tensorboard._vendor.html5lib.filters.sanitizer (top-level)
missing module named 'chardet.universaldetector' - imported by tensorboard._vendor.html5lib._inputstream (delayed, conditional, optional)
missing module named google.protobuf.enable_deterministic_proto_serialization - imported by google.protobuf (optional), google.protobuf.internal.api_implementation (optional)
missing module named google.protobuf.pyext._message - imported by google.protobuf.pyext (conditional, optional), google.protobuf.internal.api_implementation (conditional, optional), google.protobuf.descriptor (conditional), google.protobuf.pyext.cpp_message (conditional)
missing module named google.protobuf.internal._api_implementation - imported by google.protobuf.internal (optional), google.protobuf.internal.api_implementation (optional)
missing module named asyncio.DefaultEventLoopPolicy - imported by asyncio (delayed, conditional), asyncio.events (delayed, conditional)
missing module named distributed - imported by fsspec.transaction (delayed)
missing module named ujson - imported by fsspec.implementations.cache_metadata (optional), fastapi.responses (optional)
missing module named zstandard - imported by urllib3.util.request (optional), urllib3.response (optional), fsspec.compression (optional), httpx._decoders (optional)
missing module named lz4 - imported by fsspec.compression (optional)
missing module named snappy - imported by fsspec.compression (delayed, optional)
missing module named lzmaffi - imported by fsspec.compression (optional)
missing module named isal - imported by fsspec.compression (optional)
missing module named boto3 - imported by tensorboard.compat.tensorflow_stub.io.gfile (optional), gradio.utils (delayed, optional), accelerate.commands.config.sagemaker (conditional)
missing module named botocore - imported by tensorboard.compat.tensorflow_stub.io.gfile (optional)
missing module named tensorflow - imported by tensorboard.compat (delayed, optional), transformers.utils.generic (delayed, conditional), transformers.feature_extraction_utils (delayed, conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.models.encoder_decoder.modeling_tf_encoder_decoder (top-level), transformers.modeling_tf_outputs (top-level), transformers.modeling_tf_utils (top-level), transformers.activations_tf (top-level), transformers.tf_utils (top-level), safetensors.tensorflow (top-level), transformers.trainer_utils (delayed, conditional), transformers.modelcard (delayed, conditional), transformers.modeling_tf_pytorch_utils (delayed, optional), transformers.models.roformer.modeling_roformer (delayed, optional), transformers.models.roformer.modeling_tf_roformer (top-level), transformers.generation.tf_logits_process (top-level), transformers.generation.tf_utils (top-level), timm.data.readers.reader_tfds (optional), transformers.image_transforms (conditional), transformers.onnx.convert (delayed), transformers.models.albert.modeling_albert (delayed, optional), transformers.models.albert.modeling_tf_albert (top-level), transformers.models.bart.modeling_tf_bart (top-level), transformers.models.bert.modeling_bert (delayed, optional), transformers.models.bert.modeling_tf_bert (top-level), transformers.models.bert.tokenization_bert_tf (top-level), transformers.models.big_bird.modeling_big_bird (delayed, optional), transformers.models.blenderbot_small.modeling_tf_blenderbot_small (top-level), transformers.models.blenderbot.modeling_tf_blenderbot (top-level), transformers.models.blip.modeling_tf_blip (top-level), transformers.models.blip.modeling_tf_blip_text (top-level), transformers.models.camembert.modeling_tf_camembert (top-level), transformers.models.canine.modeling_canine (delayed, optional), transformers.models.clip.modeling_tf_clip (top-level), transformers.models.codegen.tokenization_codegen (conditional), transformers.models.codegen.tokenization_codegen_fast (conditional), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, conditional), transformers.models.convbert.modeling_convbert (delayed, optional), transformers.models.convbert.modeling_tf_convbert (top-level), transformers.models.convnext.modeling_tf_convnext (top-level), transformers.models.convnextv2.modeling_tf_convnextv2 (top-level), transformers.models.ctrl.modeling_tf_ctrl (top-level), transformers.models.cvt.modeling_tf_cvt (top-level), transformers.models.data2vec.modeling_tf_data2vec_vision (top-level), transformers.models.deberta.modeling_tf_deberta (top-level), transformers.models.deberta_v2.modeling_tf_deberta_v2 (top-level), transformers.models.decision_transformer.modeling_decision_transformer (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, conditional), transformers.models.deit.modeling_tf_deit (top-level), transformers.models.detr.image_processing_detr (delayed, conditional), transformers.models.distilbert.modeling_tf_distilbert (top-level), transformers.models.dpr.modeling_tf_dpr (top-level), transformers.models.electra.modeling_electra (delayed, optional), transformers.models.electra.modeling_tf_electra (top-level), transformers.models.esm.modeling_tf_esm (top-level), einops._backends (delayed), einops.layers.tensorflow (top-level), transformers.models.flaubert.modeling_tf_flaubert (top-level), transformers.models.funnel.modeling_funnel (delayed, optional), transformers.models.funnel.modeling_tf_funnel (top-level), transformers.models.gpt2.modeling_gpt2 (delayed, optional), transformers.models.gpt2.modeling_tf_gpt2 (top-level), transformers.models.gpt2.tokenization_gpt2_tf (top-level), transformers.models.gpt_neo.modeling_gpt_neo (delayed, optional), transformers.models.gptj.modeling_tf_gptj (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, conditional), transformers.models.groupvit.modeling_tf_groupvit (top-level), transformers.models.hubert.modeling_tf_hubert (top-level), transformers.models.idefics.modeling_tf_idefics (top-level), transformers.models.idefics.perceiver_tf (top-level), transformers.models.idefics.vision_tf (top-level), transformers.models.idefics.processing_idefics (conditional), transformers.models.imagegpt.modeling_imagegpt (delayed, optional), transformers.models.layoutlm.modeling_tf_layoutlm (top-level), transformers.models.layoutlmv3.modeling_tf_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_tf_xlm_roberta (top-level), transformers.models.led.modeling_tf_led (top-level), transformers.models.longformer.modeling_tf_longformer (top-level), transformers.models.lxmert.modeling_lxmert (delayed, optional), transformers.models.lxmert.modeling_tf_lxmert (top-level), transformers.models.marian.modeling_tf_marian (top-level), transformers.models.swin.modeling_tf_swin (top-level), transformers.models.mbart.modeling_tf_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (delayed, optional), transformers.models.mistral.modeling_tf_mistral (top-level), transformers.models.mobilebert.modeling_mobilebert (delayed, optional), transformers.models.mobilebert.modeling_tf_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (delayed, optional), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (delayed, optional), transformers.models.mobilevit.modeling_tf_mobilevit (top-level), transformers.models.mpnet.modeling_tf_mpnet (top-level), transformers.models.t5.modeling_t5 (delayed, optional), transformers.models.t5.modeling_tf_t5 (top-level), transformers.models.mt5.modeling_mt5 (delayed, optional), transformers.models.openai.modeling_tf_openai (top-level), transformers.models.opt.modeling_tf_opt (top-level), transformers.models.owlv2.processing_owlv2 (delayed, conditional), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_tf_pegasus (top-level), transformers.models.rag.modeling_tf_rag (top-level), transformers.models.regnet.modeling_tf_regnet (top-level), transformers.models.rembert.modeling_rembert (delayed, optional), transformers.models.rembert.modeling_tf_rembert (top-level), transformers.models.resnet.modeling_tf_resnet (top-level), transformers.models.roberta.modeling_tf_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_tf_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (delayed, optional), transformers.models.rt_detr.image_processing_rt_detr (delayed, conditional), transformers.models.sam.image_processing_sam (conditional), transformers.models.sam.modeling_tf_sam (top-level), transformers.models.sam.processing_sam (conditional), transformers.models.segformer.modeling_tf_segformer (top-level), transformers.models.speech_to_text.modeling_tf_speech_to_text (top-level), transformers.models.swiftformer.modeling_tf_swiftformer (top-level), transformers.models.tapas.modeling_tapas (delayed, optional), transformers.models.tapas.modeling_tf_tapas (top-level), transformers.models.vision_encoder_decoder.modeling_tf_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_tf_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_tf_vit (top-level), transformers.models.vit_mae.modeling_tf_vit_mae (top-level), transformers.models.wav2vec2.modeling_tf_wav2vec2 (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.whisper.modeling_tf_whisper (top-level), transformers.models.xglm.modeling_tf_xglm (top-level), transformers.models.xlm.modeling_tf_xlm (top-level), transformers.models.xlnet.modeling_tf_xlnet (top-level), transformers.models.xlnet.modeling_xlnet (delayed, optional), transformers.models.yolos.image_processing_yolos (delayed, conditional), transformers.data.data_collator (delayed, conditional), transformers.data.processors.utils (delayed, conditional), transformers.data.processors.glue (conditional), transformers.data.processors.squad (conditional), transformers.pipelines.base (conditional), transformers.pipelines.question_answering (conditional), transformers.pipelines.fill_mask (conditional), transformers.pipelines.table_question_answering (conditional), transformers.pipelines.text2text_generation (conditional), transformers.pipelines.text_generation (conditional), transformers.pipelines.token_classification (conditional), transformers.pipelines (conditional), huggingface_hub.keras_mixin (conditional, optional), huggingface_hub.serialization._tensorflow (delayed, conditional), transformers.models.deprecated.deta.image_processing_deta (delayed, conditional), transformers.models.deprecated.efficientformer.modeling_tf_efficientformer (top-level), transformers.models.deprecated.jukebox.tokenization_jukebox (delayed, conditional), transformers.models.deprecated.nezha.modeling_nezha (delayed, optional), transformers.models.deprecated.qdqbert.modeling_qdqbert (delayed, optional), transformers.models.deprecated.realm.modeling_realm (delayed, optional), transformers.models.deprecated.trajectory_transformer.modeling_trajectory_transformer (delayed, optional), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (delayed, optional), transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl (top-level), transformers.models.deprecated.transfo_xl.modeling_tf_transfo_xl_utilities (top-level), transformers.training_args_tf (conditional), transformers.benchmark.benchmark_args_tf (conditional), transformers.benchmark.benchmark_tf (conditional), transformers.keras_callbacks (top-level), transformers.optimization_tf (top-level)
missing module named tensorboard.compat.notf - imported by tensorboard.compat (delayed, optional)
missing module named '_typeshed.wsgi' - imported by httpx._transports.wsgi (conditional), werkzeug._internal (conditional), werkzeug.exceptions (conditional), werkzeug.http (conditional), werkzeug.wsgi (conditional), werkzeug.utils (conditional), werkzeug.wrappers.response (conditional), werkzeug.test (conditional), werkzeug.formparser (conditional), werkzeug.wrappers.request (conditional), werkzeug.serving (conditional), werkzeug.debug (conditional), werkzeug.middleware.shared_data (conditional)
missing module named 'tensorflow.compat' - imported by tensorboard.util.op_evaluator (delayed), tensorboard.util.encoder (delayed), tensorboard.plugins.audio.summary (delayed), tensorboard.plugins.custom_scalar.summary (delayed), tensorboard.plugins.histogram.summary (delayed), tensorboard.plugins.image.summary (delayed), tensorboard.plugins.pr_curve.summary (delayed), tensorboard.plugins.scalar.summary (delayed), tensorboard.plugins.text.summary (delayed), timm.data.tf_preprocessing (top-level), transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.deprecated.realm.retrieval_realm (delayed)
missing module named tensorboard_plugin_profile - imported by tensorboard.plugins.profile_redirect.profile_redirect_plugin (delayed, optional)
missing module named 'tensorflow.python' - imported by tensorboard.plugins.audio.summary_v2 (delayed, conditional), transformers.benchmark.benchmark_utils (conditional), transformers.benchmark.benchmark_tf (conditional), tensorboard.backend.event_processing.event_file_loader (delayed, optional), tensorboard.plugins.debugger_v2.debug_data_multiplexer (delayed, conditional, optional), torch.contrib._tensorboard_vis (optional)
missing module named grpc_reflection - imported by grpc (optional)
missing module named grpc_health - imported by grpc (optional)
missing module named grpc_tools - imported by grpc._runtime_protos (delayed, optional), grpc (optional)
missing module named 'grpc_tools.protoc' - imported by grpc._runtime_protos (delayed, conditional)
missing module named tensorflow_io - imported by tensorboard.backend.event_processing.data_ingester (delayed, conditional, optional)
missing module named 'watchdog.events' - imported by werkzeug._reloader (delayed)
missing module named watchdog - imported by werkzeug._reloader (delayed)
missing module named cryptography - imported by urllib3.contrib.pyopenssl (top-level), requests (conditional, optional), werkzeug.serving (delayed, optional)
missing module named 'cryptography.x509' - imported by urllib3.contrib.pyopenssl (delayed, optional), werkzeug.serving (delayed, conditional, optional)
missing module named 'cryptography.hazmat' - imported by werkzeug.serving (delayed, conditional, optional)
missing module named dl - imported by setuptools.command.build_ext (conditional, optional)
missing module named Cython - imported by setuptools.command.build_ext (conditional, optional)
missing module named 'com.sun' - imported by torch._appdirs (delayed, conditional, optional)
missing module named com - imported by torch._appdirs (delayed)
missing module named 'caffe2.proto' - imported by torch.utils.tensorboard.writer (delayed, conditional), torch.utils.tensorboard._caffe2_graph (top-level)
missing module named 'caffe2.python' - imported by torch.utils.tensorboard.writer (delayed, conditional), torch.utils.tensorboard._caffe2_graph (top-level), torch.testing._internal.common_utils (delayed, optional)
missing module named 'matplotlib.backends' - imported by torch.utils.tensorboard._utils (delayed), moviepy.video.io.bindings (delayed)
missing module named onnx - imported by torch.utils.tensorboard._onnx_graph (delayed), torch.onnx._internal.onnx_proto_utils (delayed, optional), torch.onnx._internal.fx.serialization (delayed, conditional), torch.onnx._internal.exporter (delayed, conditional, optional), torch.onnx._internal.fx.type_utils (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (delayed), torch.onnx._internal.onnxruntime (optional), transformers.onnx.convert (delayed), torch.onnx.verification (delayed, optional)
missing module named caffe2 - imported by torch.utils.tensorboard._convert_np (delayed)
missing module named tabulate - imported by torch.ao.ns.fx.n_shadows_utils (delayed, optional), torch._inductor.wrapper_benchmark (delayed), torch.utils.flop_counter (delayed), torch._dynamo.utils (delayed, optional), torch.fx.graph (delayed, optional), torch._dynamo.backends.distributed (delayed, conditional, optional), torch.ao.quantization.fx._model_report.model_report_visualizer (optional), torch.distributed._tensor.debug.op_coverage (delayed), torch.utils.benchmark.utils.compile (optional)
missing module named dill - imported by torch._dynamo.replay_record (optional), torch.utils.data._utils.serialization (optional), torch.utils.data.graph (delayed, conditional, optional), torch.utils.data.datapipes.datapipe (optional)
missing module named 'scipy.io' - imported by torchvision.datasets.caltech (delayed), torchvision.datasets.flowers102 (delayed), torchvision.datasets.imagenet (delayed), torchvision.datasets.sbd (delayed, optional), torchvision.datasets.stanford_cars (delayed, optional), torchvision.datasets.svhn (delayed), torch.utils.data.datapipes.utils.decoder (delayed, optional)
missing module named torchaudio - imported by torch.utils.data.datapipes.utils.decoder (delayed, optional), transformers.models.musicgen_melody.feature_extraction_musicgen_melody (conditional), transformers.pipelines.audio_classification (delayed, conditional), transformers.pipelines.automatic_speech_recognition (delayed, conditional)
missing module named accimage - imported by torchvision.datasets.folder (delayed), torchvision.transforms.transforms (optional), torchvision.transforms.functional (optional), torchvision.transforms._functional_pil (optional)
missing module named pycocotools - imported by torchvision.datasets.coco (delayed), torchvision.tv_tensors._dataset_wrapper (delayed), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, optional), transformers.models.deformable_detr.image_processing_deformable_detr_fast (delayed, optional), transformers.models.detr.image_processing_detr (delayed, optional), transformers.models.detr.image_processing_detr_fast (delayed, optional), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, optional), transformers.models.yolos.image_processing_yolos (delayed, optional), transformers.models.deprecated.deta.image_processing_deta (delayed, optional)
missing module named simplejson - imported by requests.compat (conditional, optional), huggingface_hub.utils._fixes (optional)
missing module named dummy_threading - imported by requests.cookies (optional), future.backports.http.cookiejar (optional)
missing module named 'h2.events' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.connection' - imported by urllib3.http2.connection (top-level), httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named h2 - imported by urllib3.http2.connection (top-level), httpx._client (delayed, conditional, optional)
missing module named brotli - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named brotlicffi - imported by urllib3.util.request (optional), urllib3.response (optional), httpx._decoders (optional)
missing module named socks - imported by urllib3.contrib.socks (optional)
missing module named 'OpenSSL.crypto' - imported by urllib3.contrib.pyopenssl (delayed, conditional)
missing module named OpenSSL - imported by urllib3.contrib.pyopenssl (top-level)
missing module named 'pyodide.ffi' - imported by urllib3.contrib.emscripten.fetch (delayed, optional)
missing module named pyodide - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named js - imported by urllib3.contrib.emscripten.fetch (top-level)
missing module named 'defusedxml.ElementTree' - imported by torchvision.datasets.voc (optional)
missing module named h5py - imported by transformers.modeling_tf_utils (top-level), torchvision.datasets.pcam (delayed, optional)
missing module named lmdb - imported by torchvision.datasets.lsun (delayed)
missing module named av - imported by torchvision.io.video (optional), torchvision.io.video_reader (optional), transformers.pipelines.video_classification (conditional), imageio.plugins.pyav (top-level)
missing module named torcharrow - imported by torch.utils.data.datapipes.iter.callable (delayed, conditional, optional)
missing module named 'conda.cli' - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named conda - imported by torch.utils.benchmark.examples.blas_compare_setup (optional)
missing module named 'torch._C._profiler' - imported by torch.profiler (top-level), torch.autograd.profiler (top-level), torch.testing._internal.logging_tensor (top-level), torch.utils._traceback (delayed), torch.profiler.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.cuda._memory_viz (delayed), torch.autograd (top-level), torch.profiler._pattern_matcher (top-level)
missing module named flint - imported by sympy.external.gmpy (delayed, optional), sympy.polys.polyutils (conditional), sympy.polys.factortools (conditional), sympy.polys.polyclasses (conditional), sympy.polys.domains.groundtypes (conditional), sympy.polys.domains.finitefield (conditional)
excluded module named unittest - imported by numpy.testing (top-level), doctest (top-level), numpy.testing._private.utils (top-level), torch._inductor.compile_fx (top-level), torch._dynamo.config_utils (top-level), torch._dynamo.testing (top-level), torch._dynamo.skipfiles (top-level), torch._inductor.utils (top-level), torch.testing._internal.common_utils (top-level), torch.testing._internal.common_device_type (top-level), torch._numpy.testing.utils (top-level), torch.fx.passes.tests.test_pass_manager (top-level), torch.testing._internal.opinfo.core (top-level), torch.testing._internal.opinfo.definitions._masked (top-level), torch.testing._internal.common_methods_invocations (top-level), torch.testing._internal.opinfo.definitions.linalg (top-level), torch.testing._internal.opinfo.definitions.special (top-level), torch.testing._internal.opinfo.definitions.fft (top-level), torch.testing._internal.opinfo.definitions.signal (top-level), torch.testing._internal.common_distributed (top-level), torch.testing._internal.common_fsdp (top-level), torch.testing._internal.common_modules (top-level), torch.testing._internal.common_nn (top-level), torch.testing._internal.common_quantization (top-level), torch.testing._internal.distributed.rpc.rpc_test (top-level), torch.testing._internal.distributed.rpc_utils (top-level)
missing module named py - imported by mpmath.tests.runtests (delayed, conditional)
missing module named 'matplotlib.tri' - imported by mpl_toolkits.mplot3d.axes3d (top-level)
missing module named 'matplotlib.axes' - imported by mpl_toolkits.mplot3d.axes3d (top-level), mpl_toolkits.axes_grid1.axes_size (top-level), mpl_toolkits.axes_grid1.mpl_axes (top-level)
missing module named 'matplotlib.transforms' - imported by mpl_toolkits.mplot3d.axes3d (top-level), mpl_toolkits.axes_grid1.axes_divider (top-level), mpl_toolkits.axes_grid1.parasite_axes (top-level)
missing module named 'matplotlib.container' - imported by mpl_toolkits.mplot3d.axes3d (top-level)
missing module named 'matplotlib.patches' - imported by mpl_toolkits.mplot3d.axes3d (top-level), mpl_toolkits.mplot3d.art3d (top-level), networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.lines' - imported by mpl_toolkits.mplot3d.axes3d (top-level)
missing module named 'matplotlib.image' - imported by mpl_toolkits.mplot3d.axes3d (top-level), skimage.io._plugins.matplotlib_plugin (delayed)
missing module named 'matplotlib.colors' - imported by mpl_toolkits.mplot3d.axes3d (top-level), mpl_toolkits.mplot3d.art3d (top-level), networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.collections' - imported by mpl_toolkits.mplot3d.axes3d (top-level), mpl_toolkits.mplot3d.art3d (top-level), networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.artist' - imported by mpl_toolkits.mplot3d.axes3d (top-level), mpl_toolkits.axes_grid1.mpl_axes (top-level), mpl_toolkits.axes_grid1.parasite_axes (top-level)
missing module named 'matplotlib.pylab' - imported by pylab (top-level)
missing module named 'sage.libs' - imported by mpmath.libmp.backend (conditional, optional), mpmath.libmp.libelefun (conditional, optional), mpmath.libmp.libmpf (conditional, optional), mpmath.libmp.libmpc (conditional, optional), mpmath.libmp.libhyper (delayed, conditional), mpmath.ctx_mp (conditional)
missing module named sage - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy - imported by mpmath.libmp.backend (conditional, optional)
missing module named gmpy2 - imported by mpmath.libmp.backend (conditional, optional), sympy.polys.domains.groundtypes (conditional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.image' - imported by sympy.printing.preview (delayed, optional)
missing module named 'pyglet.window' - imported by sympy.plotting.pygletplot.managed_window (top-level), sympy.plotting.pygletplot.plot_controller (top-level), sympy.printing.preview (delayed, optional)
missing module named pyglet - imported by sympy.plotting.pygletplot.plot (optional), sympy.plotting.pygletplot.plot_axes (top-level), sympy.printing.preview (delayed, conditional, optional), sympy.testing.runtests (delayed, conditional)
missing module named 'pyglet.gl' - imported by sympy.plotting.pygletplot.plot_axes (top-level), sympy.plotting.pygletplot.util (top-level), sympy.plotting.pygletplot.plot_window (top-level), sympy.plotting.pygletplot.plot_camera (top-level), sympy.plotting.pygletplot.plot_rotation (top-level), sympy.plotting.pygletplot.plot_curve (top-level), sympy.plotting.pygletplot.plot_mode_base (top-level), sympy.plotting.pygletplot.plot_surface (top-level)
missing module named 'pyglet.clock' - imported by sympy.plotting.pygletplot.managed_window (top-level)
excluded module named pytest - imported by sympy.testing.runtests_pytest (optional), networkx.utils.backends (delayed, conditional, optional), torch.testing._internal.common_utils (delayed, conditional), skimage._shared.tester (delayed), skimage.data._fetchers (delayed, conditional), torch._numpy.testing.utils (delayed)
missing module named all - imported by sympy.testing.runtests (delayed, optional)
missing module named 'IPython.Shell' - imported by sympy.interactive.session (delayed, conditional)
missing module named 'IPython.frontend' - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional)
missing module named 'IPython.terminal' - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional)
missing module named 'IPython.iplib' - imported by sympy.interactive.printing (delayed, optional)
missing module named 'IPython.core' - imported by sympy.interactive.printing (delayed, optional), rich.pretty (delayed, optional), dotenv.ipython (top-level), gradio.utils (delayed, optional), altair.utils.core (delayed, conditional), altair._magics (top-level), gradio.ipython_ext (optional)
excluded module named IPython - imported by sympy.interactive.printing (delayed, conditional, optional), sympy.interactive.session (delayed, conditional, optional)
missing module named 'IPython.lib' - imported by sympy.interactive.printing (delayed, optional)
missing module named 'scipy.special' - imported by sympy.functions.special.bessel (delayed, conditional, optional), networkx.generators.community (delayed, optional), transformers.models.conditional_detr.image_processing_conditional_detr (conditional), transformers.models.deformable_detr.image_processing_deformable_detr (conditional), transformers.models.detr.image_processing_detr (conditional), transformers.models.grounding_dino.image_processing_grounding_dino (conditional), transformers.models.yolos.image_processing_yolos (conditional), torch.testing._internal.opinfo.definitions.special (conditional), torch.testing._internal.common_methods_invocations (conditional)
missing module named 'scipy.optimize' - imported by sympy.functions.special.bessel (delayed, conditional), transformers.models.mask2former.modeling_mask2former (conditional), transformers.models.maskformer.modeling_maskformer (conditional), transformers.models.oneformer.modeling_oneformer (conditional), transformers.loss.loss_for_object_detection (conditional), transformers.loss.loss_deformable_detr (conditional), transformers.loss.loss_rt_detr (conditional), transformers.models.deprecated.deta.modeling_deta (conditional)
missing module named pysat - imported by sympy.logic.algorithms.minisat22_wrapper (delayed)
missing module named pycosat - imported by sympy.logic.algorithms.pycosat_wrapper (delayed)
missing module named 'sage.all' - imported by sympy.core.function (delayed)
missing module named 'sage.interfaces' - imported by sympy.core.basic (delayed)
missing module named 'hypothesis.strategies' - imported by torch.testing._internal.hypothesis_utils (top-level)
missing module named 'hypothesis.extra' - imported by torch.testing._internal.hypothesis_utils (top-level)
missing module named hypothesis - imported by torch.testing._internal.common_utils (optional), torch.testing._internal.hypothesis_utils (top-level)
missing module named 'torch._C._distributed_c10d' - imported by torch.distributed (conditional), torch.distributed.distributed_c10d (top-level), torch.distributed.constants (top-level), torch.distributed.rpc (conditional), torch.distributed._shard.sharded_tensor.reshard (top-level), torch.distributed._shard.sharding_spec.chunk_sharding_spec_ops.embedding_bag (top-level), torch._dynamo.variables.distributed (delayed), torch.testing._internal.distributed.multi_threaded_pg (top-level), torch.testing._internal.distributed.fake_pg (top-level)
missing module named 'scipy.signal' - imported by torch.testing._internal.opinfo.definitions.signal (conditional)
missing module named 'scipy.fft' - imported by torch.testing._internal.opinfo.definitions.fft (conditional, optional)
missing module named 'xmlrunner.result' - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named xmlrunner - imported by torch.testing._internal.common_utils (delayed, conditional)
missing module named torch.nn.Sequential - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ParameterDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleList - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named torch.nn.ModuleDict - imported by torch.nn (top-level), torch.testing._internal.common_utils (top-level)
missing module named expecttest - imported by torch.testing._internal.common_utils (top-level)
missing module named 'scipy.spatial' - imported by torch.testing._internal.common_methods_invocations (conditional)
excluded module named scipy - imported by networkx.convert (delayed, optional), networkx.convert_matrix (delayed), networkx.generators.geometric (delayed, optional), networkx.algorithms.assortativity.correlation (delayed), networkx.algorithms.centrality.flow_matrix (delayed), networkx.algorithms.centrality.eigenvector (delayed), networkx.algorithms.centrality.subgraph_alg (delayed), networkx.algorithms.centrality.laplacian (delayed), networkx.algorithms.communicability_alg (delayed), networkx.algorithms.distance_measures (delayed), networkx.algorithms.link_analysis.hits_alg (delayed), networkx.algorithms.link_analysis.pagerank_alg (delayed), networkx.algorithms.similarity (delayed), networkx.algorithms.approximation.traveling_salesman (delayed), networkx.algorithms.bipartite.matrix (delayed), networkx.algorithms.bipartite.matching (delayed), networkx.algorithms.bipartite.spectral (delayed), networkx.algorithms.node_classification (delayed), networkx.generators.random_graphs (delayed, conditional), networkx.generators.spectral_graph_forge (delayed), networkx.linalg.attrmatrix (delayed), networkx.linalg.spectrum (delayed), networkx.linalg.graphmatrix (delayed), networkx.linalg.laplacianmatrix (delayed), networkx.linalg.algebraicconnectivity (delayed, conditional), networkx.linalg.bethehessianmatrix (delayed), networkx.drawing.layout (delayed), timm.layers.pos_embed_rel (delayed, conditional), transformers.models.fnet.modeling_fnet (conditional), transformers.models.owlv2.image_processing_owlv2 (conditional), diffusers.schedulers.scheduling_lms_discrete_flax (top-level), diffusers.schedulers.scheduling_lms_discrete (top-level), skimage._shared.filters (top-level), skimage.color.colorconv (top-level), torch.testing._internal.common_methods_invocations (conditional)
missing module named bitsandbytes - imported by accelerate.utils.imports (delayed), transformers.utils.import_utils (delayed), peft.import_utils (delayed), peft.utils.loftq_utils (conditional), peft.tuners.lora.bnb (top-level), peft.tuners.lora.model (delayed, conditional), peft.tuners.ia3.model (delayed, conditional), peft.tuners.adalora.model (delayed, conditional), accelerate.utils.bnb (delayed), transformers.models.rwkv.modeling_rwkv (delayed), transformers.quantizers.quantizer_bnb_4bit (delayed), transformers.quantizers.quantizer_bnb_8bit (delayed), transformers.modeling_utils (delayed, conditional), transformers.integrations.bitsandbytes (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named intel_extension_for_pytorch - imported by accelerate.utils.imports (delayed, conditional), transformers.utils.import_utils (delayed, conditional), transformers.trainer (delayed), accelerate.accelerator (delayed, conditional)
missing module named torch_musa - imported by accelerate.utils.imports (delayed), transformers.utils.import_utils (delayed), accelerate.utils.modeling (conditional), accelerate.state (conditional)
missing module named torch_mlu - imported by accelerate.utils.imports (delayed), transformers.utils.import_utils (delayed), accelerate.utils.modeling (conditional), accelerate.state (conditional)
missing module named torch_npu - imported by accelerate.utils.imports (delayed), transformers.utils.import_utils (delayed), accelerate.utils.modeling (conditional), accelerate.state (conditional), accelerate.accelerator (conditional), diffusers.pipelines.pipeline_utils (conditional), diffusers.training_utils (conditional)
missing module named torch_xla - imported by accelerate.utils.imports (conditional, optional), transformers.utils.import_utils (delayed), transformers.pytorch_utils (delayed, conditional), peft.utils.other (delayed, conditional), transformers.trainer (conditional), accelerate.launchers (delayed, conditional), huggingface_hub.serialization._torch (delayed, conditional)
missing module named 'torch_xla.core' - imported by accelerate.optimizer (conditional), transformers.utils.import_utils (delayed, conditional, optional), transformers.trainer_utils (delayed, conditional), transformers.trainer_pt_utils (delayed, conditional), transformers.training_args (conditional), accelerate.utils.operations (conditional), accelerate.utils.other (conditional), peft.import_utils (delayed, conditional, optional), transformers.trainer (conditional), accelerate.state (conditional), accelerate.utils.random (conditional), accelerate.checkpointing (conditional), accelerate.accelerator (conditional), diffusers.pipelines.stable_diffusion_xl.pipeline_stable_diffusion_xl (conditional), diffusers.pipelines.stable_diffusion_xl.pipeline_stable_diffusion_xl_img2img (conditional), diffusers.pipelines.stable_diffusion_xl.pipeline_stable_diffusion_xl_inpaint (conditional), diffusers.pipelines.stable_diffusion_xl.pipeline_stable_diffusion_xl_instruct_pix2pix (conditional), diffusers.pipelines.ledits_pp.pipeline_leditspp_stable_diffusion_xl (conditional), huggingface_hub.serialization._torch (delayed, conditional, optional), transformers.benchmark.benchmark_args (conditional), torch._dynamo.backends.torchxla (delayed, optional)
missing module named mamba_ssm - imported by transformers.utils.import_utils (delayed, conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named mlx - imported by transformers.utils.generic (delayed)
missing module named 'google.colab' - imported by huggingface_hub.utils._token (delayed, optional)
missing module named eval_type_backport - imported by pydantic._internal._typing_extra (delayed, optional)
missing module named annotationlib - imported by attr._compat (conditional)
missing module named linkify_it - imported by markdown_it.main (optional)
missing module named cython - imported by pydantic.v1.version (optional)
missing module named email_validator - imported by pydantic.networks (delayed, conditional, optional), pydantic.v1.networks (delayed, conditional, optional), fastapi.openapi.models (optional)
missing module named pydantic.PydanticUserError - imported by pydantic (top-level), pydantic.root_model (top-level)
missing module named pydantic.PydanticSchemaGenerationError - imported by pydantic (delayed), pydantic.functional_validators (delayed, conditional), fastapi._compat (conditional)
missing module named pydantic.BaseModel - imported by pydantic (conditional), pydantic._internal._typing_extra (conditional), pydantic._internal._import_utils (delayed, conditional), pydantic.deprecated.copy_internals (delayed, conditional), huggingface_hub._webhooks_payload (conditional), fastapi.exceptions (top-level), fastapi.types (top-level), fastapi._compat (top-level), fastapi.openapi.models (top-level), fastapi.security.http (top-level), fastapi.utils (top-level), fastapi.encoders (top-level), fastapi.routing (top-level), gradio.data_classes (conditional)
missing module named hf_transfer - imported by huggingface_hub.file_download (delayed, conditional, optional), huggingface_hub.lfs (delayed, optional)
missing module named tf_keras - imported by transformers.activations_tf (optional), transformers.modeling_tf_utils (optional), huggingface_hub.keras_mixin (conditional, optional)
missing module named aiohttp - imported by huggingface_hub.inference._common (delayed, conditional), huggingface_hub.inference._generated._async_client (conditional)
missing module named fastai - imported by huggingface_hub.fastai_utils (delayed)
missing module named toml - imported by huggingface_hub.fastai_utils (delayed, optional)
missing module named apport_python_hook - imported by exceptiongroup._formatting (conditional)
missing module named curio - imported by sniffio._impl (delayed, conditional)
missing module named 'trio.testing' - imported by anyio._backends._trio (delayed)
missing module named 'trio.to_thread' - imported by anyio._backends._trio (top-level)
missing module named 'trio.socket' - imported by anyio._backends._trio (top-level)
missing module named outcome - imported by anyio._backends._trio (top-level)
missing module named 'trio.lowlevel' - imported by anyio._backends._trio (top-level)
missing module named 'trio.from_thread' - imported by anyio._backends._trio (top-level)
missing module named _pytest - imported by anyio._backends._asyncio (delayed)
missing module named uvloop - imported by anyio._backends._asyncio (delayed, conditional), uvicorn.loops.auto (delayed, optional), uvicorn.loops.uvloop (top-level)
missing module named asyncio.Runner - imported by asyncio (conditional), anyio._backends._asyncio (conditional)
missing module named dirty_equals - imported by fastapi.utils (delayed)
missing module named gradio.ChatMessage - imported by gradio (delayed, optional), transformers.agents.monitoring (delayed, optional)
missing module named flax - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.generation.flax_utils (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), diffusers.schedulers.scheduling_ddim_flax (top-level), diffusers.schedulers.scheduling_utils_flax (top-level), diffusers.schedulers.scheduling_ddpm_flax (top-level), diffusers.schedulers.scheduling_dpmsolver_multistep_flax (top-level), diffusers.schedulers.scheduling_euler_discrete_flax (top-level), diffusers.schedulers.scheduling_karras_ve_flax (top-level), diffusers.schedulers.scheduling_lms_discrete_flax (top-level), diffusers.schedulers.scheduling_pndm_flax (top-level), diffusers.schedulers.scheduling_sde_ve_flax (top-level), diffusers.pipelines.stable_diffusion.pipeline_output (conditional), diffusers.pipelines.pipeline_flax_utils (top-level), diffusers.pipelines.stable_diffusion.safety_checker_flax (top-level), diffusers.models.unets.unet_2d_condition_flax (top-level), diffusers.models.controlnet_flax (top-level), diffusers.models.vae_flax (top-level), diffusers.pipelines.stable_diffusion_xl.pipeline_output (conditional)
missing module named 'transformer_engine.common' - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named 'intel_transformer_engine.recipe' - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named 'transformer_engine.pytorch' - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named transformer_engine - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named intel_transformer_engine - imported by accelerate.utils.transformer_engine (delayed, conditional)
missing module named torch_ccl - imported by accelerate.state (delayed, conditional)
missing module named oneccl_bindings_for_pytorch - imported by accelerate.state (delayed, conditional)
missing module named 'smdistributed.dataparallel' - imported by accelerate.state (delayed, conditional)
missing module named datasets - imported by transformers.modeling_tf_utils (delayed), transformers.modelcard (delayed, conditional), timm.data.readers.reader_hfds (optional), timm.data.readers.reader_hfids (optional), transformers.models.rag.retrieval_rag (conditional), transformers.trainer (conditional), accelerate.state (delayed, conditional), transformers.agents.text_to_speech (conditional), transformers.trainer_seq2seq (conditional)
missing module named deepspeed - imported by accelerate.utils.deepspeed (delayed), accelerate.utils.operations (delayed, conditional), accelerate.utils.other (delayed, conditional), transformers.models.distilbert.modeling_distilbert (delayed, conditional), transformers.models.esm.modeling_esmfold (delayed, conditional, optional), transformers.models.fsmt.modeling_fsmt (delayed, conditional), transformers.models.hubert.modeling_hubert (delayed, conditional), transformers.models.seamless_m4t.modeling_seamless_m4t (delayed, conditional), transformers.models.sew.modeling_sew (delayed, conditional), transformers.models.sew_d.modeling_sew_d (delayed, conditional), transformers.models.speecht5.modeling_speecht5 (delayed, conditional), transformers.models.unispeech.modeling_unispeech (delayed, conditional), transformers.models.unispeech_sat.modeling_unispeech_sat (delayed, conditional), transformers.models.wav2vec2.modeling_wav2vec2 (delayed, conditional), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (delayed, conditional), transformers.models.wavlm.modeling_wavlm (delayed, conditional), transformers.modeling_utils (delayed, conditional), accelerate.state (delayed, conditional), accelerate.accelerator (delayed), diffusers.training_utils (delayed, conditional)
missing module named torch_sdaa - imported by accelerate.utils.imports (delayed), accelerate.utils.modeling (conditional), accelerate.state (conditional)
missing module named 'intel_extension_for_pytorch.xpu' - imported by accelerate.utils.memory (delayed, conditional)
missing module named 'torch_xla.distributed' - imported by transformers.training_args (conditional), transformers.integrations.tpu (delayed, conditional), transformers.trainer (delayed, conditional, optional), accelerate.data_loader (conditional), accelerate.accelerator (conditional), accelerate.launchers (delayed, conditional)
missing module named torchdata - imported by accelerate.data_loader (delayed, conditional)
missing module named pretrain_t5 - imported by accelerate.utils.megatron_lm (delayed, conditional, optional)
missing module named pretrain_gpt - imported by accelerate.utils.megatron_lm (delayed, conditional, optional)
missing module named pretrain_bert - imported by accelerate.utils.megatron_lm (delayed, conditional, optional)
missing module named 'megatron.training' - imported by accelerate.utils.megatron_lm (conditional)
missing module named 'megatron.legacy' - imported by accelerate.utils.megatron_lm (conditional)
missing module named 'megatron.inference' - imported by accelerate.utils.megatron_lm (conditional)
missing module named 'megatron.core' - imported by accelerate.utils.megatron_lm (conditional)
missing module named lomo_optim - imported by accelerate.optimizer (delayed, conditional), transformers.trainer (delayed, conditional), accelerate.accelerator (delayed, conditional)
missing module named torch.nn.MSELoss - imported by torch.nn (top-level), transformers.models.roformer.modeling_roformer (top-level), peft.peft_model (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gpt_neox.modeling_gpt_neox (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.loss.loss_utils (top-level), accelerate.utils.megatron_lm (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level)
missing module named torch.nn.CrossEntropyLoss - imported by torch.nn (top-level), transformers.modeling_utils (top-level), transformers.models.encoder_decoder.modeling_encoder_decoder (top-level), transformers.models.roformer.modeling_roformer (top-level), peft.peft_model (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.blenderbot.modeling_blenderbot (top-level), transformers.models.blenderbot_small.modeling_blenderbot_small (top-level), transformers.models.blip.modeling_blip_text (top-level), transformers.models.blip_2.modeling_blip_2 (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.bridgetower.modeling_bridgetower (top-level), transformers.models.bros.modeling_bros (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.chameleon.modeling_chameleon (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.clvp.modeling_clvp (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.cpmant.modeling_cpmant (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_audio (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.dpt.modeling_dpt (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.falcon_mamba.modeling_falcon_mamba (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gpt_neox.modeling_gpt_neox (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.hubert.modeling_hubert (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.idefics.modeling_idefics (top-level), transformers.models.idefics2.modeling_idefics2 (top-level), transformers.models.idefics3.modeling_idefics3 (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.instructblip.modeling_instructblip (top-level), transformers.models.instructblipvideo.modeling_instructblipvideo (top-level), transformers.models.kosmos2.modeling_kosmos2 (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.longt5.modeling_longt5 (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.lxmert.modeling_lxmert (top-level), transformers.models.m2m_100.modeling_m2m_100 (top-level), transformers.models.mamba.modeling_mamba (top-level), transformers.models.mamba2.modeling_mamba2 (top-level), transformers.models.marian.modeling_marian (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.moshi.modeling_moshi (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.musicgen.modeling_musicgen (top-level), transformers.models.musicgen_melody.modeling_musicgen_melody (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nllb_moe.modeling_nllb_moe (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.pegasus.modeling_pegasus (top-level), transformers.models.pegasus_x.modeling_pegasus_x (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pop2piano.modeling_pop2piano (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.qwen2_vl.modeling_qwen2_vl (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.seamless_m4t.modeling_seamless_m4t (top-level), transformers.models.seamless_m4t_v2.modeling_seamless_m4t_v2 (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.sew.modeling_sew (top-level), transformers.models.sew_d.modeling_sew_d (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.speech_encoder_decoder.modeling_speech_encoder_decoder (top-level), transformers.models.speech_to_text.modeling_speech_to_text (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.splinter.modeling_splinter (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.switch_transformers.modeling_switch_transformers (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.trocr.modeling_trocr (top-level), transformers.models.udop.modeling_udop (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.unispeech.modeling_unispeech (top-level), transformers.models.unispeech_sat.modeling_unispeech_sat (top-level), transformers.models.upernet.modeling_upernet (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vilt.modeling_vilt (top-level), transformers.models.vision_encoder_decoder.modeling_vision_encoder_decoder (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.vivit.modeling_vivit (top-level), transformers.models.wav2vec2.modeling_wav2vec2 (top-level), transformers.models.wav2vec2_bert.modeling_wav2vec2_bert (top-level), transformers.models.wav2vec2_conformer.modeling_wav2vec2_conformer (top-level), transformers.models.wavlm.modeling_wavlm (top-level), transformers.models.whisper.modeling_whisper (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), accelerate.utils.megatron_lm (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.mmbt.modeling_mmbt (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.realm.modeling_realm (top-level), transformers.models.deprecated.speech_to_text_2.modeling_speech_to_text_2 (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level)
missing module named torch.nn.BCEWithLogitsLoss - imported by torch.nn (top-level), transformers.models.roformer.modeling_roformer (top-level), peft.peft_model (top-level), transformers.models.albert.modeling_albert (top-level), transformers.models.audio_spectrogram_transformer.modeling_audio_spectrogram_transformer (top-level), transformers.models.bart.modeling_bart (top-level), transformers.models.beit.modeling_beit (top-level), transformers.models.bert.modeling_bert (top-level), transformers.models.big_bird.modeling_big_bird (top-level), transformers.models.bigbird_pegasus.modeling_bigbird_pegasus (top-level), transformers.models.biogpt.modeling_biogpt (top-level), transformers.models.bit.modeling_bit (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.camembert.modeling_camembert (top-level), transformers.models.canine.modeling_canine (top-level), transformers.models.clip.modeling_clip (top-level), transformers.models.convbert.modeling_convbert (top-level), transformers.models.convnext.modeling_convnext (top-level), transformers.models.convnextv2.modeling_convnextv2 (top-level), transformers.models.ctrl.modeling_ctrl (top-level), transformers.models.cvt.modeling_cvt (top-level), transformers.models.data2vec.modeling_data2vec_text (top-level), transformers.models.data2vec.modeling_data2vec_vision (top-level), transformers.models.deberta.modeling_deberta (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.deit.modeling_deit (top-level), transformers.models.dinat.modeling_dinat (top-level), transformers.models.dinov2.modeling_dinov2 (top-level), transformers.models.dinov2_with_registers.modeling_dinov2_with_registers (top-level), transformers.models.distilbert.modeling_distilbert (top-level), transformers.models.efficientnet.modeling_efficientnet (top-level), transformers.models.electra.modeling_electra (top-level), transformers.models.ernie.modeling_ernie (top-level), transformers.models.esm.modeling_esm (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.flaubert.modeling_flaubert (top-level), transformers.models.fnet.modeling_fnet (top-level), transformers.models.focalnet.modeling_focalnet (top-level), transformers.models.funnel.modeling_funnel (top-level), transformers.models.gpt2.modeling_gpt2 (top-level), transformers.models.gpt_bigcode.modeling_gpt_bigcode (top-level), transformers.models.gpt_neo.modeling_gpt_neo (top-level), transformers.models.gpt_neox.modeling_gpt_neox (top-level), transformers.models.gptj.modeling_gptj (top-level), transformers.models.hiera.modeling_hiera (top-level), transformers.models.ibert.modeling_ibert (top-level), transformers.models.ijepa.modeling_ijepa (top-level), transformers.models.imagegpt.modeling_imagegpt (top-level), transformers.models.layoutlm.modeling_layoutlm (top-level), transformers.models.layoutlmv2.modeling_layoutlmv2 (top-level), transformers.models.layoutlmv3.modeling_layoutlmv3 (top-level), transformers.models.xlm_roberta.modeling_xlm_roberta (top-level), transformers.models.led.modeling_led (top-level), transformers.models.levit.modeling_levit (top-level), transformers.models.lilt.modeling_lilt (top-level), transformers.models.longformer.modeling_longformer (top-level), transformers.models.luke.modeling_luke (top-level), transformers.models.markuplm.modeling_markuplm (top-level), transformers.models.swin.modeling_swin (top-level), transformers.models.mbart.modeling_mbart (top-level), transformers.models.megatron_bert.modeling_megatron_bert (top-level), transformers.models.mobilebert.modeling_mobilebert (top-level), transformers.models.mobilenet_v1.modeling_mobilenet_v1 (top-level), transformers.models.mobilenet_v2.modeling_mobilenet_v2 (top-level), transformers.models.mobilevit.modeling_mobilevit (top-level), transformers.models.mobilevitv2.modeling_mobilevitv2 (top-level), transformers.models.modernbert.modeling_modernbert (top-level), transformers.models.mpnet.modeling_mpnet (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.mra.modeling_mra (top-level), transformers.models.t5.modeling_t5 (top-level), transformers.models.mt5.modeling_mt5 (top-level), transformers.models.mvp.modeling_mvp (top-level), transformers.models.nystromformer.modeling_nystromformer (top-level), transformers.models.openai.modeling_openai (top-level), transformers.models.opt.modeling_opt (top-level), transformers.models.perceiver.modeling_perceiver (top-level), transformers.models.plbart.modeling_plbart (top-level), transformers.models.poolformer.modeling_poolformer (top-level), transformers.models.pvt.modeling_pvt (top-level), transformers.models.pvt_v2.modeling_pvt_v2 (top-level), transformers.models.reformer.modeling_reformer (top-level), transformers.models.regnet.modeling_regnet (top-level), transformers.models.rembert.modeling_rembert (top-level), transformers.models.resnet.modeling_resnet (top-level), transformers.models.roberta.modeling_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_roberta_prelayernorm (top-level), transformers.models.roc_bert.modeling_roc_bert (top-level), transformers.models.segformer.modeling_segformer (top-level), transformers.models.siglip.modeling_siglip (top-level), transformers.models.speecht5.modeling_speecht5 (top-level), transformers.models.squeezebert.modeling_squeezebert (top-level), transformers.models.swiftformer.modeling_swiftformer (top-level), transformers.models.swinv2.modeling_swinv2 (top-level), transformers.models.tapas.modeling_tapas (top-level), transformers.models.textnet.modeling_textnet (top-level), transformers.models.timesformer.modeling_timesformer (top-level), transformers.models.timm_wrapper.modeling_timm_wrapper (top-level), transformers.models.umt5.modeling_umt5 (top-level), transformers.models.videomae.modeling_videomae (top-level), transformers.models.vit.modeling_vit (top-level), transformers.models.vit_msn.modeling_vit_msn (top-level), transformers.models.xlm.modeling_xlm (top-level), transformers.models.xlm_roberta_xl.modeling_xlm_roberta_xl (top-level), transformers.models.xlnet.modeling_xlnet (top-level), transformers.models.xmod.modeling_xmod (top-level), transformers.models.yoso.modeling_yoso (top-level), transformers.models.zamba.modeling_zamba (top-level), transformers.loss.loss_utils (top-level), accelerate.utils.megatron_lm (top-level), transformers.models.deprecated.efficientformer.modeling_efficientformer (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), transformers.models.deprecated.graphormer.modeling_graphormer (top-level), transformers.models.deprecated.mega.modeling_mega (top-level), transformers.models.deprecated.nat.modeling_nat (top-level), transformers.models.deprecated.nezha.modeling_nezha (top-level), transformers.models.deprecated.open_llama.modeling_open_llama (top-level), transformers.models.deprecated.qdqbert.modeling_qdqbert (top-level), transformers.models.deprecated.transfo_xl.modeling_transfo_xl (top-level), transformers.models.deprecated.tvlt.modeling_tvlt (top-level), transformers.models.deprecated.van.modeling_van (top-level), transformers.models.deprecated.vit_hybrid.modeling_vit_hybrid (top-level)
missing module named 'torch.distributed.checkpoint.format_utils' - imported by accelerate.utils.fsdp_utils (delayed)
missing module named auto_gptq - imported by peft.utils.other (delayed, conditional)
missing module named 'scipy.stats' - imported by peft.utils.loftq_utils (delayed, optional), transformers.models.conditional_detr.image_processing_conditional_detr (conditional), transformers.models.deformable_detr.image_processing_deformable_detr (conditional), transformers.models.detr.image_processing_detr (conditional), transformers.models.esm.modeling_esmfold (delayed, conditional), transformers.models.grounding_dino.image_processing_grounding_dino (conditional), transformers.models.yolos.image_processing_yolos (conditional), transformers.data.metrics (conditional)
missing module named 'deepspeed.ops' - imported by accelerate.utils.deepspeed (delayed, conditional)
missing module named 'bitsandbytes.optim' - imported by accelerate.utils.deepspeed (delayed, conditional), transformers.trainer (delayed, conditional, optional)
missing module named 'torch_xla.amp' - imported by accelerate.utils.modeling (delayed, conditional), transformers.trainer (delayed, conditional, optional)
missing module named 'habana_frameworks.torch' - imported by accelerate.utils.imports (delayed, conditional)
missing module named habana_frameworks - imported by accelerate.utils.imports (delayed)
missing module named 'torch_xla.runtime' - imported by accelerate.utils.imports (conditional, optional), transformers.trainer (conditional)
missing module named pynvml - imported by torch.cuda (delayed, optional), accelerate.utils.environment (delayed, conditional), torch.cuda.memory (delayed, optional)
missing module named megatron - imported by accelerate.utils.dataclasses (delayed)
missing module named 'torch.distributed.device_mesh' - imported by accelerate.utils.dataclasses (delayed)
missing module named 'deepspeed.utils' - imported by transformers.integrations.deepspeed (delayed), accelerate.utils.dataclasses (delayed, conditional)
missing module named 'transformers.deepspeed' - imported by accelerate.utils.dataclasses (delayed, conditional)
missing module named torchao - imported by accelerate.utils.dataclasses (conditional)
missing module named 'torchao.float8' - imported by accelerate.utils.ao (delayed, conditional)
missing module named 'torch.distributed.pipelining' - imported by accelerate.inference (delayed)
missing module named 'deepspeed.checkpoint' - imported by accelerate.accelerator (delayed, conditional)
missing module named msamp - imported by accelerate.accelerator (delayed, conditional)
missing module named 'dvclive.plots' - imported by transformers.integrations.integration_utils (delayed, conditional), accelerate.tracking (delayed)
missing module named dvclive - imported by transformers.integrations.integration_utils (delayed), accelerate.tracking (delayed)
missing module named clearml - imported by transformers.integrations.integration_utils (delayed, conditional), accelerate.tracking (delayed)
missing module named mlflow - imported by transformers.integrations.integration_utils (delayed), accelerate.tracking (delayed)
missing module named aim - imported by accelerate.tracking (delayed)
missing module named comet_ml - imported by transformers.integrations.integration_utils (delayed, conditional, optional), accelerate.tracking (delayed)
missing module named wandb - imported by timm.utils.summary (optional), transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional), accelerate.tracking (delayed)
missing module named tensorboardX - imported by huggingface_hub._tensorboard_logger (conditional, optional), transformers.integrations.integration_utils (delayed, conditional, optional), accelerate.tracking (delayed, optional)
missing module named 'xformers.ops' - imported by diffusers.models.attention_processor (conditional)
missing module named xformers - imported by diffusers.models.attention_processor (conditional), transformers.models.deprecated.open_llama.modeling_open_llama (optional)
missing module named imwatermark - imported by diffusers.pipelines.stable_diffusion_xl.watermark (conditional)
missing module named tf2onnx - imported by transformers.onnx.convert (delayed)
missing module named onnxruntime - imported by torch.onnx._internal.onnxruntime (optional), transformers.onnx.convert (delayed, optional), diffusers.pipelines.onnx_utils (conditional), torch.onnx.verification (delayed, optional)
missing module named 'flax.training' - imported by diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion_img2img (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion_inpaint (top-level), diffusers.pipelines.controlnet.pipeline_flax_controlnet (top-level)
missing module named 'flax.jax_utils' - imported by diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion_img2img (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion_inpaint (top-level), diffusers.pipelines.controlnet.pipeline_flax_controlnet (top-level)
missing module named flash_attn - imported by transformers.modeling_flash_attention_utils (conditional), transformers.models.qwen2_vl.modeling_qwen2_vl (conditional)
missing module named ftfy - imported by transformers.models.clip.tokenization_clip (delayed, optional), transformers.models.openai.tokenization_openai (delayed, optional), diffusers.pipelines.deepfloyd_if.pipeline_if (conditional), diffusers.pipelines.deepfloyd_if.pipeline_if_img2img (conditional), diffusers.pipelines.deepfloyd_if.pipeline_if_img2img_superresolution (conditional), diffusers.pipelines.deepfloyd_if.pipeline_if_inpainting (conditional), diffusers.pipelines.deepfloyd_if.pipeline_if_inpainting_superresolution (conditional), diffusers.pipelines.deepfloyd_if.pipeline_if_superresolution (conditional), diffusers.pipelines.pixart_alpha.pipeline_pixart_alpha (conditional)
missing module named bs4 - imported by transformers.models.markuplm.feature_extraction_markuplm (conditional), diffusers.pipelines.deepfloyd_if.pipeline_if (conditional), diffusers.pipelines.deepfloyd_if.pipeline_if_img2img (conditional), diffusers.pipelines.deepfloyd_if.pipeline_if_img2img_superresolution (conditional), diffusers.pipelines.deepfloyd_if.pipeline_if_inpainting (conditional), diffusers.pipelines.deepfloyd_if.pipeline_if_inpainting_superresolution (conditional), diffusers.pipelines.deepfloyd_if.pipeline_if_superresolution (conditional), diffusers.pipelines.pixart_alpha.pipeline_pixart_alpha (conditional)
missing module named 'msgpack.exceptions' - imported by diffusers.models.modeling_flax_utils (top-level)
missing module named torchsde - imported by diffusers.schedulers.scheduling_dpmsolver_sde (top-level)
missing module named diffusers.schedulers.utils - imported by diffusers.schedulers.deprecated (conditional, optional)
missing module named 'k_diffusion.sampling' - imported by diffusers.pipelines.stable_diffusion_k_diffusion.pipeline_stable_diffusion_k_diffusion (top-level), diffusers.pipelines.stable_diffusion_k_diffusion.pipeline_stable_diffusion_xl_k_diffusion (top-level)
missing module named 'k_diffusion.external' - imported by diffusers.pipelines.stable_diffusion_k_diffusion.pipeline_stable_diffusion_xl_k_diffusion (top-level)
missing module named k_diffusion - imported by diffusers.pipelines.stable_diffusion_k_diffusion.pipeline_stable_diffusion_k_diffusion (top-level)
missing module named librosa - imported by diffusers.pipelines.deprecated.audio_diffusion.mel (optional), diffusers.pipelines.audioldm2.pipeline_audioldm2 (conditional), diffusers.pipelines.musicldm.pipeline_musicldm (conditional)
missing module named note_seq - imported by diffusers.pipelines.deprecated.spectrogram_diffusion.midi_utils (conditional)
missing module named 'apex.normalization' - imported by timm.layers.fast_norm (optional), transformers.models.longt5.modeling_longt5 (optional), transformers.models.t5.modeling_t5 (optional), transformers.models.pix2struct.modeling_pix2struct (optional), transformers.models.pop2piano.modeling_pop2piano (optional)
missing module named gradio_client.handle_file - imported by gradio_client (delayed), transformers.agents.tools (delayed)
missing module named asyncio.timeout - imported by asyncio (conditional), websockets.legacy.compatibility (conditional)
missing module named trio - imported by httpx._transports.asgi (delayed, conditional), httpcore._synchronization (optional), httpcore._backends.trio (top-level)
missing module named socksio - imported by httpcore._sync.socks_proxy (top-level), httpcore._async.socks_proxy (top-level), httpx._transports.default (delayed, conditional, optional)
missing module named 'h2.settings' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.exceptions' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'h2.config' - imported by httpcore._sync.http2 (top-level), httpcore._async.http2 (top-level)
missing module named 'matplotlib.animation' - imported by gradio.helpers (delayed)
missing module named 'twine.settings' - imported by gradio.cli.commands.components.publish (delayed, conditional, optional)
missing module named twine - imported by gradio.cli.commands.components.publish (delayed, conditional, optional)
missing module named spaces - imported by gradio.blocks (optional)
missing module named 'pandas.api' - imported by altair.utils.core (delayed), gradio.components.scatter_plot (top-level)
missing module named rpds.List - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieSet - imported by rpds (top-level), referencing._core (top-level)
missing module named rpds.HashTrieMap - imported by rpds (top-level), referencing._core (top-level), jsonschema._types (top-level), jsonschema.validators (top-level)
missing module named 'pyarrow.csv' - imported by narwhals._arrow.dataframe (delayed)
missing module named 'pyarrow.parquet' - imported by narwhals._arrow.dataframe (delayed), narwhals.functions (delayed, conditional)
missing module named dask_expr - imported by narwhals._dask.utils (delayed, optional), narwhals._dask.expr_dt (conditional, optional), narwhals._dask.expr (conditional, optional), narwhals._dask.selectors (conditional, optional), narwhals._dask.namespace (conditional, optional), narwhals._dask.group_by (optional)
missing module named 'dask_expr._groupby' - imported by narwhals._dask.group_by (conditional)
missing module named 'dask.array' - imported by narwhals._dask.expr (delayed), skimage.util.apply_parallel (delayed, optional)
missing module named dask - imported by narwhals.dependencies (conditional), narwhals._arrow.dataframe (delayed, conditional), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._polars.dataframe (delayed, conditional)
missing module named 'polars.lazyframe' - imported by narwhals._polars.group_by (conditional)
missing module named 'polars.dataframe' - imported by narwhals._polars.group_by (conditional)
missing module named 'duckdb.typing' - imported by narwhals._duckdb.expr (top-level), narwhals._duckdb.namespace (top-level)
missing module named 'pyarrow.types' - imported by narwhals._arrow.utils (conditional)
missing module named 'pyarrow._stubs_typing' - imported by narwhals._arrow.typing (conditional)
missing module named 'pyarrow.compute' - imported by narwhals._arrow.expr (top-level), narwhals._arrow.series (top-level), narwhals._arrow.utils (top-level), narwhals._arrow.dataframe (top-level), narwhals._arrow.group_by (top-level), narwhals._arrow.namespace (top-level), narwhals._pandas_like.series_dt (delayed, conditional), narwhals._arrow.series_dt (top-level), narwhals._arrow.series_list (top-level), narwhals._arrow.series_str (top-level), narwhals._arrow.series_struct (top-level)
missing module named 'pyarrow.interchange' - imported by narwhals._interchange.dataframe (delayed), altair.utils._importers (delayed, optional)
missing module named 'pandas._typing' - imported by narwhals._pandas_like.utils (conditional)
missing module named sqlframe - imported by narwhals._spark_like.utils (conditional), narwhals.utils (delayed, conditional)
missing module named duckdb - imported by narwhals.dependencies (conditional), narwhals._polars.dataframe (delayed, conditional), narwhals._duckdb.dataframe (top-level), narwhals._duckdb.utils (top-level), narwhals._duckdb.expr (top-level), narwhals._duckdb.expr_dt (top-level), narwhals._duckdb.expr_list (top-level), narwhals._duckdb.expr_str (top-level), narwhals._duckdb.expr_struct (top-level), narwhals._duckdb.namespace (top-level), narwhals._duckdb.selectors (conditional), narwhals._duckdb.typing (conditional), narwhals._duckdb.group_by (conditional), narwhals._duckdb.series (conditional), narwhals._arrow.dataframe (delayed, conditional), narwhals._pandas_like.dataframe (delayed, conditional), narwhals.translate (delayed, conditional), narwhals.utils (delayed, conditional)
missing module named 'dask.dataframe' - imported by narwhals._arrow.dataframe (delayed, conditional), narwhals._dask.dataframe (top-level), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._dask.utils (delayed, conditional, optional), narwhals._dask.expr_dt (conditional, optional), narwhals._dask.expr_str (top-level), narwhals._dask.expr (conditional, optional), narwhals._dask.namespace (top-level), narwhals._dask.selectors (conditional, optional), narwhals._dask.group_by (top-level), narwhals._polars.dataframe (delayed, conditional), narwhals.utils (delayed, conditional)
missing module named polars - imported by narwhals.dependencies (conditional), narwhals.series (conditional), narwhals._polars.dataframe (top-level), narwhals._polars.namespace (top-level), narwhals._polars.expr (top-level), narwhals._polars.utils (top-level), narwhals._polars.series (top-level), narwhals._arrow.dataframe (delayed, conditional), narwhals._pandas_like.series (delayed, conditional), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._compliant.series (conditional), narwhals._dask.dataframe (delayed, conditional), narwhals._duckdb.dataframe (delayed, conditional), narwhals._spark_like.dataframe (delayed, conditional), narwhals.schema (delayed, conditional), narwhals.dataframe (conditional), narwhals.functions (conditional), narwhals._compliant.dataframe (conditional), narwhals.utils (delayed, conditional), narwhals._arrow.series (delayed, conditional), gradio.components.dataframe (delayed, conditional)
missing module named 'pyspark.sql' - imported by narwhals._spark_like.utils (delayed, conditional), narwhals.utils (delayed, conditional)
missing module named pyarrow - imported by narwhals.dependencies (conditional), narwhals._arrow.typing (conditional), narwhals._arrow.series (top-level), narwhals._arrow.series_cat (top-level), narwhals._arrow.utils (top-level), narwhals.series (delayed, optional), narwhals._polars.dataframe (delayed, conditional), narwhals._duckdb.dataframe (delayed, conditional), narwhals._arrow.dataframe (top-level), narwhals._arrow.group_by (top-level), narwhals._arrow.namespace (top-level), narwhals._pandas_like.series (delayed), narwhals._pandas_like.dataframe (delayed, conditional), narwhals._pandas_like.utils (delayed, conditional, optional), narwhals._dask.dataframe (delayed, conditional), narwhals._ibis.dataframe (conditional), narwhals._spark_like.dataframe (delayed, conditional), narwhals._interchange.dataframe (conditional), narwhals.schema (delayed, conditional), narwhals.dataframe (delayed, conditional, optional), narwhals.functions (delayed, conditional, optional), narwhals._compliant.dataframe (conditional), narwhals.utils (delayed, conditional), narwhals._arrow.series_dt (top-level), narwhals._arrow.series_list (top-level), altair.utils.data (delayed, conditional)
missing module named cudf - imported by narwhals.dependencies (conditional), narwhals.utils (delayed, conditional)
missing module named 'modin.pandas' - imported by narwhals.functions (delayed, conditional), narwhals.utils (delayed, conditional)
missing module named 'sqlframe._version' - imported by narwhals.translate (delayed, conditional)
missing module named 'sqlframe.base' - imported by narwhals._spark_like.utils (delayed, conditional), narwhals._spark_like.expr_dt (conditional), narwhals._spark_like.expr_str (conditional), narwhals._spark_like.expr_struct (conditional), narwhals._spark_like.expr (delayed, conditional), narwhals._spark_like.selectors (conditional), narwhals._spark_like.namespace (conditional), narwhals._spark_like.typing (conditional), narwhals._spark_like.dataframe (delayed, conditional), narwhals._spark_like.group_by (conditional), narwhals.dependencies (delayed, conditional)
missing module named 'pyspark.pandas' - imported by narwhals._spark_like.utils (delayed, conditional)
missing module named ibis - imported by narwhals.dependencies (conditional), narwhals.translate (delayed, conditional)
missing module named 'ibis.selectors' - imported by narwhals._ibis.dataframe (top-level)
missing module named pyspark - imported by narwhals.dependencies (conditional)
missing module named modin - imported by narwhals.dependencies (conditional)
missing module named isoduration - imported by jsonschema._format (top-level)
missing module named uri_template - imported by jsonschema._format (top-level)
missing module named jsonpointer - imported by jsonschema._format (top-level)
missing module named webcolors - imported by jsonschema._format (top-level)
missing module named rfc3339_validator - imported by jsonschema._format (top-level)
missing module named rfc3986_validator - imported by jsonschema._format (optional)
missing module named rfc3987 - imported by jsonschema._format (optional)
missing module named fqdn - imported by jsonschema._format (top-level)
missing module named 'vegafusion.runtime' - imported by altair.utils._vegafusion_data (conditional)
missing module named altair.vegalite.SCHEMA_VERSION - imported by altair.vegalite (delayed), altair.utils._importers (delayed)
missing module named vl_convert - imported by altair.utils._importers (delayed, optional)
missing module named vegafusion - imported by altair.utils._importers (delayed, optional)
missing module named altair.vegalite.v5.SCHEMA_VERSION - imported by altair.vegalite.v5 (delayed), altair.vegalite.v5.compiler (delayed)
missing module named traitlets - imported by altair.jupyter.jupyter_chart (top-level)
missing module named anywidget - imported by altair.jupyter (optional), altair.jupyter.jupyter_chart (top-level)
missing module named altair.VConcatSpecGenericSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.VConcatChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.UnitSpecWithFrame - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.UnitSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelVConcatSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelUnitSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelLayerSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelHConcatSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelFacetSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.TopLevelConcatSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.NonNormalizedSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.LayerSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.LayerChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.HConcatSpecGenericSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.HConcatChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.FacetSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.FacetedUnitSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.FacetChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.ConcatSpecGenericSpec - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.ConcatChart - imported by altair (top-level), altair.utils._transformed_data (top-level)
missing module named altair.Chart - imported by altair (delayed), altair.vegalite.v5.display (delayed), altair.utils._transformed_data (top-level)
missing module named altair.renderers - imported by altair (delayed), altair.utils.mimebundle (delayed)
missing module named altair.vegalite_compilers - imported by altair (delayed), altair.utils._vegafusion_data (delayed)
missing module named altair.data_transformers - imported by altair (delayed), altair.utils._vegafusion_data (delayed), altair.utils._transformed_data (top-level)
missing module named altair.SchemaBase - imported by altair (conditional), altair.vegalite.v5.schema.channels (conditional)
missing module named altair.Parameter - imported by altair (conditional), altair.vegalite.v5.schema.core (conditional), altair.vegalite.v5.schema.channels (conditional), altair.vegalite.v5.schema.mixins (conditional)
missing module named 'bokeh.embed' - imported by gradio.components.plot (delayed, conditional)
missing module named 'matplotlib.figure' - imported by gradio.components.plot (delayed)
missing module named bokeh - imported by gradio.components.plot (delayed, optional)
missing module named 'pandas.io' - imported by gradio.components.dataframe (top-level)
missing module named a2wsgi - imported by uvicorn.middleware.wsgi (optional)
missing module named 'gunicorn.workers' - imported by uvicorn.workers (top-level)
missing module named gunicorn - imported by uvicorn.workers (top-level)
missing module named watchfiles - imported by uvicorn.supervisors.watchfilesreload (top-level)
missing module named httptools - imported by uvicorn.protocols.http.httptools_impl (top-level), uvicorn.protocols.http.auto (optional)
missing module named authlib - imported by gradio.oauth (delayed, optional)
missing module named pyaudioop - imported by pydub.utils (optional)
missing module named 'pyodide.http' - imported by gradio.analytics (optional)
missing module named 'ipywidgets.widgets' - imported by huggingface_hub._login (delayed, optional)
missing module named 'InquirerPy.separator' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'InquirerPy.base' - imported by huggingface_hub.commands.delete_cache (optional)
missing module named InquirerPy - imported by huggingface_hub.commands.delete_cache (optional)
missing module named 'jax.random' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), diffusers.models.modeling_flax_pytorch_utils (top-level)
missing module named 'flax.traverse_util' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), diffusers.models.modeling_pytorch_flax_utils (top-level), diffusers.models.modeling_flax_utils (top-level), diffusers.models.modeling_flax_pytorch_utils (top-level)
missing module named 'flax.serialization' - imported by transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), diffusers.models.modeling_pytorch_flax_utils (top-level), diffusers.models.modeling_flax_utils (top-level)
missing module named 'flax.core' - imported by transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion (top-level), diffusers.pipelines.pipeline_flax_utils (top-level), diffusers.models.modeling_flax_utils (top-level), diffusers.pipelines.stable_diffusion.safety_checker_flax (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion_img2img (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion_inpaint (top-level), diffusers.models.unets.unet_2d_condition_flax (top-level), diffusers.models.controlnet_flax (top-level), diffusers.models.vae_flax (top-level), diffusers.pipelines.stable_diffusion_xl.pipeline_flax_stable_diffusion_xl (top-level), diffusers.pipelines.controlnet.pipeline_flax_controlnet (top-level)
missing module named msgpack - imported by transformers.modeling_flax_utils (top-level)
missing module named 'jax.numpy' - imported by transformers.utils.generic (delayed, conditional), transformers.feature_extraction_utils (delayed, conditional), transformers.tokenization_utils_base (delayed, conditional), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_outputs (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.generation.flax_logits_process (top-level), transformers.generation.flax_utils (top-level), timm.models.vision_transformer (delayed, conditional), transformers.image_transforms (conditional), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.conditional_detr.image_processing_conditional_detr (delayed, conditional), transformers.models.deformable_detr.image_processing_deformable_detr (delayed, conditional), transformers.models.detr.image_processing_detr (delayed, conditional), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), einops._backends (delayed), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.grounding_dino.image_processing_grounding_dino (delayed, conditional), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.mt5.modeling_flax_mt5 (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.owlv2.processing_owlv2 (delayed, conditional), transformers.models.owlvit.processing_owlvit (delayed, conditional), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.rt_detr.image_processing_rt_detr (delayed, conditional), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.wav2vec2.tokenization_wav2vec2 (conditional), transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (conditional), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), transformers.models.yolos.image_processing_yolos (delayed, conditional), diffusers.models.modeling_pytorch_flax_utils (top-level), diffusers.schedulers.scheduling_ddim_flax (top-level), diffusers.schedulers.scheduling_utils_flax (top-level), diffusers.schedulers.scheduling_ddpm_flax (top-level), diffusers.schedulers.scheduling_dpmsolver_multistep_flax (top-level), diffusers.schedulers.scheduling_euler_discrete_flax (top-level), diffusers.schedulers.scheduling_karras_ve_flax (top-level), diffusers.schedulers.scheduling_lms_discrete_flax (top-level), diffusers.schedulers.scheduling_pndm_flax (top-level), diffusers.schedulers.scheduling_sde_ve_flax (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion (top-level), diffusers.models.modeling_flax_utils (top-level), diffusers.models.modeling_flax_pytorch_utils (top-level), diffusers.pipelines.stable_diffusion.safety_checker_flax (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion_img2img (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion_inpaint (top-level), diffusers.models.unets.unet_2d_condition_flax (top-level), diffusers.models.embeddings_flax (top-level), diffusers.models.unets.unet_2d_blocks_flax (top-level), diffusers.models.attention_flax (top-level), diffusers.models.resnet_flax (top-level), diffusers.models.controlnet_flax (top-level), diffusers.models.vae_flax (top-level), diffusers.pipelines.stable_diffusion_xl.pipeline_flax_stable_diffusion_xl (top-level), diffusers.pipelines.controlnet.pipeline_flax_controlnet (top-level), transformers.models.deprecated.deta.image_processing_deta (delayed, conditional), transformers.models.deprecated.jukebox.tokenization_jukebox (delayed, conditional)
missing module named jax - imported by transformers.utils.generic (delayed, conditional), transformers.models.encoder_decoder.modeling_flax_encoder_decoder (top-level), transformers.modeling_flax_utils (top-level), transformers.modeling_flax_pytorch_utils (top-level), safetensors.flax (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.generation.flax_logits_process (top-level), transformers.generation.flax_utils (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), diffusers.models.modeling_pytorch_flax_utils (top-level), diffusers.schedulers.scheduling_ddpm_flax (top-level), diffusers.schedulers.scheduling_dpmsolver_multistep_flax (top-level), diffusers.schedulers.scheduling_karras_ve_flax (top-level), diffusers.schedulers.scheduling_pndm_flax (top-level), diffusers.schedulers.scheduling_sde_ve_flax (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion (top-level), diffusers.models.modeling_flax_utils (top-level), diffusers.pipelines.stable_diffusion.safety_checker_flax (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion_img2img (top-level), diffusers.pipelines.stable_diffusion.pipeline_flax_stable_diffusion_inpaint (top-level), diffusers.models.unets.unet_2d_condition_flax (top-level), diffusers.models.attention_flax (top-level), diffusers.models.resnet_flax (top-level), diffusers.models.controlnet_flax (top-level), diffusers.models.vae_flax (top-level), diffusers.pipelines.stable_diffusion_xl.pipeline_flax_stable_diffusion_xl (top-level), diffusers.pipelines.controlnet.pipeline_flax_controlnet (top-level)
missing module named 'flax.linen' - imported by transformers.modeling_flax_utils (top-level), transformers.models.roformer.modeling_flax_roformer (top-level), transformers.models.albert.modeling_flax_albert (top-level), transformers.models.bart.modeling_flax_bart (top-level), transformers.models.beit.modeling_flax_beit (top-level), transformers.models.bert.modeling_flax_bert (top-level), transformers.models.big_bird.modeling_flax_big_bird (top-level), transformers.models.blenderbot_small.modeling_flax_blenderbot_small (top-level), transformers.models.blenderbot.modeling_flax_blenderbot (top-level), transformers.models.bloom.modeling_flax_bloom (top-level), transformers.models.clip.modeling_flax_clip (top-level), transformers.models.dinov2.modeling_flax_dinov2 (top-level), transformers.models.distilbert.modeling_flax_distilbert (top-level), transformers.models.electra.modeling_flax_electra (top-level), transformers.models.gemma.modeling_flax_gemma (top-level), transformers.models.gpt2.modeling_flax_gpt2 (top-level), transformers.models.gpt_neo.modeling_flax_gpt_neo (top-level), transformers.models.gptj.modeling_flax_gptj (top-level), transformers.models.xlm_roberta.modeling_flax_xlm_roberta (top-level), transformers.models.llama.modeling_flax_llama (top-level), transformers.models.longt5.modeling_flax_longt5 (top-level), transformers.models.marian.modeling_flax_marian (top-level), transformers.models.mbart.modeling_flax_mbart (top-level), transformers.models.mistral.modeling_flax_mistral (top-level), transformers.models.t5.modeling_flax_t5 (top-level), transformers.models.opt.modeling_flax_opt (top-level), transformers.models.pegasus.modeling_flax_pegasus (top-level), transformers.models.regnet.modeling_flax_regnet (top-level), transformers.models.resnet.modeling_flax_resnet (top-level), transformers.models.roberta.modeling_flax_roberta (top-level), transformers.models.roberta_prelayernorm.modeling_flax_roberta_prelayernorm (top-level), transformers.models.speech_encoder_decoder.modeling_flax_speech_encoder_decoder (top-level), transformers.models.vision_encoder_decoder.modeling_flax_vision_encoder_decoder (top-level), transformers.models.vision_text_dual_encoder.modeling_flax_vision_text_dual_encoder (top-level), transformers.models.vit.modeling_flax_vit (top-level), transformers.models.wav2vec2.modeling_flax_wav2vec2 (top-level), transformers.models.whisper.modeling_flax_whisper (top-level), transformers.models.xglm.modeling_flax_xglm (top-level), diffusers.models.unets.unet_2d_condition_flax (top-level), diffusers.models.embeddings_flax (top-level), diffusers.models.unets.unet_2d_blocks_flax (top-level), diffusers.models.attention_flax (top-level), diffusers.models.resnet_flax (top-level), diffusers.models.controlnet_flax (top-level), diffusers.models.vae_flax (top-level)
missing module named transformers.models.pop2piano.Pop2PianoTokenizer - imported by transformers.models.pop2piano (conditional, optional), transformers (conditional, optional)
missing module named transformers.models.pop2piano.Pop2PianoProcessor - imported by transformers.models.pop2piano (conditional, optional), transformers (conditional, optional)
missing module named transformers.models.pop2piano.Pop2PianoFeatureExtractor - imported by transformers.models.pop2piano (conditional, optional), transformers (conditional, optional)
missing module named 'tensorflow.keras' - imported by einops.layers.tensorflow (top-level), transformers.optimization_tf (optional)
missing module named 'tf_keras.optimizers' - imported by transformers.optimization_tf (optional)
missing module named keras - imported by transformers.activations_tf (optional), transformers.modeling_tf_utils (optional)
missing module named 'py3nvml.py3nvml' - imported by transformers.benchmark.benchmark (conditional), transformers.benchmark.benchmark_tf (conditional)
missing module named py3nvml - imported by transformers.benchmark.benchmark_utils (conditional)
missing module named torch.cuda.empty_cache - imported by torch.cuda (conditional), transformers.benchmark.benchmark_utils (conditional)
missing module named smdistributed - imported by transformers.trainer_pt_utils (conditional)
missing module named 'torch_xla.experimental' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'ray.train' - imported by transformers.integrations.integration_utils (delayed), transformers.trainer (delayed, conditional)
missing module named schedulefree - imported by transformers.trainer (delayed, conditional)
missing module named 'torchao.prototype' - imported by transformers.trainer (delayed, conditional)
missing module named grokadamw - imported by transformers.trainer (delayed, conditional)
missing module named galore_torch - imported by transformers.trainer (delayed, conditional)
missing module named 'torchdistx.optimizers' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'apex.optimizers' - imported by transformers.trainer (delayed, conditional, optional)
missing module named 'torch_npu.optim' - imported by transformers.trainer (delayed, conditional, optional)
missing module named liger_kernel - imported by transformers.trainer (delayed, conditional)
missing module named optuna - imported by transformers.integrations.integration_utils (delayed, conditional), transformers.trainer (delayed, conditional)
missing module named 'smdistributed.modelparallel' - imported by transformers.training_args (conditional), transformers.modeling_utils (conditional), transformers.trainer (conditional)
missing module named 'torch_xla.debug' - imported by transformers.trainer (conditional), transformers.benchmark.benchmark (delayed, conditional, optional)
missing module named apex - imported by timm.layers.fast_norm (optional), timm.utils.cuda (optional), transformers.trainer (conditional)
missing module named torch.distributed.tensor.Replicate - imported by torch.distributed.tensor (conditional), transformers.pytorch_utils (conditional)
missing module named 'optimum.bettertransformer' - imported by transformers.modeling_utils (delayed)
missing module named 'optimum.version' - imported by transformers.modeling_utils (delayed)
missing module named gguf - imported by transformers.modeling_gguf_pytorch_utils (delayed, conditional)
missing module named vptq - imported by transformers.quantizers.quantizer_vptq (delayed, conditional), transformers.integrations.vptq (top-level)
missing module named 'torchao.quantization' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_torchao (delayed)
missing module named 'torchao.dtypes' - imported by transformers.quantizers.quantizer_torchao (delayed)
missing module named 'optimum.quanto' - imported by transformers.cache_utils (delayed, conditional), transformers.quantizers.quantizer_quanto (delayed, conditional), transformers.integrations.quanto (delayed, conditional)
missing module named 'hqq.core' - imported by transformers.utils.quantization_config (delayed, conditional), transformers.quantizers.quantizer_hqq (delayed, conditional), transformers.integrations.hqq (delayed, conditional)
missing module named flute - imported by transformers.quantizers.quantizer_higgs (delayed)
missing module named 'optimum.gptq' - imported by transformers.quantizers.quantizer_gptq (delayed)
missing module named eetq - imported by transformers.quantizers.quantizer_eetq (delayed, optional), transformers.integrations.eetq (conditional)
missing module named 'compressed_tensors.config' - imported by transformers.quantizers.quantizer_compressed_tensors (delayed)
missing module named 'compressed_tensors.quantization' - imported by transformers.utils.quantization_config (delayed), transformers.quantizers.quantizer_compressed_tensors (delayed, conditional)
missing module named 'compressed_tensors.compressors' - imported by transformers.quantizers.quantizer_compressed_tensors (delayed)
missing module named 'torch.nn.attention' - imported by transformers.models.gpt_neox.modeling_gpt_neox (conditional), transformers.integrations.flex_attention (conditional)
missing module named torch.nn.Identity - imported by torch.nn (top-level), transformers.modeling_utils (top-level)
missing module named 'torch.export._trace' - imported by transformers.integrations.executorch (delayed)
missing module named optimum - imported by transformers.cache_utils (delayed, conditional)
missing module named hqq - imported by transformers.cache_utils (conditional)
missing module named transformers.models.timm_wrapper.TimmWrapperImageProcessor - imported by transformers.models.timm_wrapper (conditional, optional), transformers (conditional, optional)
missing module named tiktoken - imported by transformers.convert_slow_tokenizer (delayed, optional)
missing module named rjieba - imported by transformers.models.roformer.tokenization_roformer (delayed, optional), transformers.models.roformer.tokenization_utils (delayed, optional)
missing module named sentencepiece - imported by transformers.convert_slow_tokenizer (delayed, conditional), transformers.models.albert.tokenization_albert (top-level), transformers.models.barthez.tokenization_barthez (top-level), transformers.models.bartpho.tokenization_bartpho (top-level), transformers.models.bert_generation.tokenization_bert_generation (top-level), transformers.models.bert_japanese.tokenization_bert_japanese (conditional), transformers.models.big_bird.tokenization_big_bird (top-level), transformers.models.camembert.tokenization_camembert (top-level), transformers.models.code_llama.tokenization_code_llama (top-level), transformers.models.cpm.tokenization_cpm (top-level), transformers.models.deberta_v2.tokenization_deberta_v2 (top-level), transformers.models.fnet.tokenization_fnet (top-level), transformers.models.gemma.tokenization_gemma (top-level), transformers.models.gpt_sw3.tokenization_gpt_sw3 (top-level), transformers.models.layoutxlm.tokenization_layoutxlm (top-level), transformers.models.xlm_roberta.tokenization_xlm_roberta (top-level), transformers.models.llama.tokenization_llama (top-level), transformers.models.m2m_100.tokenization_m2m_100 (top-level), transformers.models.marian.tokenization_marian (top-level), transformers.models.mbart.tokenization_mbart (top-level), transformers.models.mbart50.tokenization_mbart50 (top-level), transformers.models.mluke.tokenization_mluke (top-level), transformers.models.t5.tokenization_t5 (top-level), transformers.models.nllb.tokenization_nllb (top-level), transformers.models.pegasus.tokenization_pegasus (top-level), transformers.models.plbart.tokenization_plbart (top-level), transformers.models.reformer.tokenization_reformer (top-level), transformers.models.rembert.tokenization_rembert (top-level), transformers.models.seamless_m4t.tokenization_seamless_m4t (top-level), transformers.models.siglip.tokenization_siglip (top-level), transformers.models.speech_to_text.tokenization_speech_to_text (top-level), transformers.models.speecht5.tokenization_speecht5 (top-level), transformers.models.udop.tokenization_udop (top-level), transformers.models.xglm.tokenization_xglm (top-level), transformers.models.xlnet.tokenization_xlnet (top-level), transformers.models.deprecated.ernie_m.tokenization_ernie_m (top-level), transformers.models.deprecated.xlm_prophetnet.tokenization_xlm_prophetnet (delayed, optional)
missing module named sentencepiece_model_pb2 - imported by tokenizers.implementations.sentencepiece_unigram (delayed, optional)
missing module named 'transformers.utils.dummies_sentencepiece_and_tokenizers_objects' - imported by transformers (conditional, optional)
missing module named transformers.models.mt5.MT5TokenizerFast - imported by transformers.models.mt5 (conditional, optional), transformers (conditional, optional)
missing module named jieba - imported by transformers.models.cpm.tokenization_cpm (delayed, optional), transformers.models.cpm.tokenization_cpm_fast (delayed, optional), transformers.models.cpmant.tokenization_cpmant (conditional), transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named compressed_tensors - imported by transformers.utils.quantization_config (delayed)
missing module named ray - imported by transformers.trainer_utils (delayed), transformers.integrations.integration_utils (delayed)
missing module named 'mlx.core' - imported by transformers.tokenization_utils_base (delayed, conditional)
missing module named causal_conv1d - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional)
missing module named 'mamba_ssm.ops' - imported by transformers.models.jamba.modeling_jamba (conditional), transformers.models.bamba.modeling_bamba (conditional), transformers.models.falcon_mamba.modeling_falcon_mamba (conditional), transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (conditional), transformers.models.mamba.modeling_mamba (conditional), transformers.models.mamba2.modeling_mamba2 (conditional), transformers.models.zamba.modeling_zamba (conditional)
missing module named fast_lsh_cumulation - imported by transformers.models.yoso.modeling_yoso (delayed)
missing module named pythainlp - imported by transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named Mykytea - imported by transformers.models.flaubert.tokenization_flaubert (delayed, conditional, optional), transformers.models.herbert.tokenization_herbert (delayed, conditional, optional), transformers.models.xlm.tokenization_xlm (delayed, conditional, optional)
missing module named sacremoses - imported by transformers.models.biogpt.tokenization_biogpt (delayed, optional), transformers.models.flaubert.tokenization_flaubert (delayed, optional), transformers.models.fsmt.tokenization_fsmt (delayed, optional), transformers.models.herbert.tokenization_herbert (delayed, optional), transformers.models.marian.tokenization_marian (delayed, optional), transformers.models.xlm.tokenization_xlm (delayed, optional), transformers.models.deprecated.transfo_xl.tokenization_transfo_xl (conditional)
missing module named regex.DEFAULT_VERSION - imported by regex (delayed, optional), regex.regex (delayed, optional)
missing module named 'jax.experimental' - imported by transformers.generation.flax_logits_process (top-level)
missing module named 'jax.lax' - imported by transformers.generation.flax_logits_process (top-level)
missing module named 'pyctcdecode.constants' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named 'pyctcdecode.alphabet' - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed)
missing module named pyctcdecode - imported by transformers.models.wav2vec2_with_lm.processing_wav2vec2_with_lm (delayed, conditional), transformers.pipelines.automatic_speech_recognition (conditional), transformers.pipelines (delayed, conditional, optional)
missing module named 'phonemizer.separator' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named 'phonemizer.backend' - imported by transformers.models.wav2vec2_phoneme.tokenization_wav2vec2_phoneme (delayed)
missing module named uroman - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named phonemizer - imported by transformers.models.vits.tokenization_vits (conditional)
missing module named 'scipy.ndimage' - imported by transformers.models.vitpose.image_processing_vitpose (conditional), moviepy.video.fx.painting (optional), moviepy.video.tools.segmenting (top-level)
missing module named 'scipy.linalg' - imported by transformers.models.vitpose.image_processing_vitpose (conditional)
missing module named torch.nn.LogSoftmax - imported by torch.nn (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level)
missing module named torch.nn.KLDivLoss - imported by torch.nn (top-level), transformers.models.visual_bert.modeling_visual_bert (top-level)
missing module named transformers.models.timm_wrapper.processing_timm_wrapper - imported by transformers.models.timm_wrapper (conditional)
missing module named inplace_abn - imported by timm.layers.inplace_abn (optional)
missing module named 'webdataset.tariterators' - imported by timm.data.readers.reader_wds (optional)
missing module named 'webdataset.shardlists' - imported by timm.data.readers.reader_wds (optional)
missing module named 'webdataset.filters' - imported by timm.data.readers.reader_wds (optional)
missing module named webdataset - imported by timm.data.readers.reader_wds (optional)
missing module named tensorflow_datasets - imported by timm.data.readers.reader_tfds (optional)
missing module named 'datasets.splits' - imported by timm.data.readers.reader_hfids (optional)
missing module named 'datasets.distributed' - imported by timm.data.readers.reader_hfids (optional)
missing module named ml_dtypes - imported by timm.models.vision_transformer (delayed, conditional)
missing module named tensorflow_probability - imported by transformers.models.groupvit.modeling_tf_groupvit (conditional, optional), transformers.models.tapas.modeling_tf_tapas (conditional, optional)
missing module named 'tensorflow.compiler' - imported by transformers.generation.tf_utils (top-level), transformers.models.t5.modeling_tf_t5 (top-level)
missing module named torch.nn.L1Loss - imported by torch.nn (top-level), transformers.models.speecht5.modeling_speecht5 (top-level)
missing module named 'torchaudio.compliance' - imported by transformers.models.audio_spectrogram_transformer.feature_extraction_audio_spectrogram_transformer (conditional), transformers.models.speech_to_text.feature_extraction_speech_to_text (conditional)
missing module named torch.nn.LayerNorm - imported by torch.nn (top-level), transformers.models.bloom.modeling_bloom (top-level), transformers.models.deberta_v2.modeling_deberta_v2 (top-level), transformers.models.esm.modeling_esmfold (top-level), transformers.models.falcon.modeling_falcon (top-level), transformers.models.fsmt.modeling_fsmt (top-level), transformers.models.mpt.modeling_mpt (top-level), transformers.models.prophetnet.modeling_prophetnet (top-level), transformers.models.qwen2_vl.modeling_qwen2_vl (top-level), transformers.models.sew_d.modeling_sew_d (top-level), transformers.models.deprecated.jukebox.modeling_jukebox (top-level), transformers.models.deprecated.xlm_prophetnet.modeling_xlm_prophetnet (top-level)
missing module named 'tensorflow.experimental' - imported by transformers.models.sam.image_processing_sam (conditional)
missing module named faiss - imported by transformers.models.rag.retrieval_rag (conditional)
missing module named 'flash_attn.bert_padding' - imported by transformers.models.chameleon.modeling_chameleon (conditional), transformers.models.paligemma.modeling_paligemma (conditional)
missing module named spacy - imported by transformers.models.openai.tokenization_openai (delayed, optional)
missing module named nltk - imported by transformers.models.nougat.tokenization_nougat_fast (conditional)
missing module named Levenshtein - imported by transformers.models.nougat.tokenization_nougat_fast (conditional)
missing module named 'flash_attn.ops' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named 'flash_attn.layers' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named 'flash_attn.flash_attn_interface' - imported by transformers.models.modernbert.modeling_modernbert (conditional)
missing module named 'mambapy.pscan' - imported by transformers.models.mamba.modeling_mamba (conditional)
missing module named torch.nn.SmoothL1Loss - imported by torch.nn (top-level), transformers.models.lxmert.modeling_lxmert (top-level)
missing module named pytesseract - imported by transformers.models.layoutlmv2.image_processing_layoutlmv2 (conditional), transformers.models.layoutlmv3.image_processing_layoutlmv3 (conditional), transformers.pipelines.document_question_answering (conditional)
missing module named 'detectron2.modeling' - imported by transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named detectron2 - imported by transformers.models.layoutlmv2.configuration_layoutlmv2 (conditional), transformers.models.layoutlmv2.modeling_layoutlmv2 (conditional)
missing module named tensorflow_text - imported by transformers.models.bert.tokenization_bert_tf (top-level), transformers.models.bert_generation.modeling_bert_generation (delayed, optional), transformers.models.gpt2.tokenization_gpt2_tf (top-level)
missing module named keras_nlp - imported by transformers.models.gpt2.tokenization_gpt2_tf (top-level)
missing module named transformers.models.funnel.convert_funnel_original_tf_checkpoint_to_pytorch - imported by transformers.models.funnel (conditional)
missing module named g2p_en - imported by transformers.models.fastspeech2_conformer.tokenization_fastspeech2_conformer (delayed, optional)
missing module named selective_scan_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (top-level)
missing module named causal_conv1d_cuda - imported by transformers.kernels.falcon_mamba.selective_scan_with_ln_interface (optional)
missing module named pytensor - imported by einops._backends (delayed)
missing module named tinygrad - imported by einops._backends (delayed)
missing module named paddle - imported by einops._backends (delayed), einops.layers.paddle (top-level)
missing module named oneflow - imported by einops._backends (delayed), einops.layers.oneflow (top-level)
missing module named cupy - imported by einops._backends (delayed)
missing module named mambapy - imported by transformers.models.falcon_mamba.modeling_falcon_mamba (conditional)
missing module named natten - imported by transformers.models.dinat.modeling_dinat (conditional)
missing module named scann - imported by transformers.models.deprecated.realm.retrieval_realm (delayed)
missing module named 'pytorch_quantization.nn' - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named pytorch_quantization - imported by transformers.models.deprecated.qdqbert.modeling_qdqbert (conditional, optional)
missing module named 'natten.functional' - imported by transformers.models.deprecated.nat.modeling_nat (conditional)
missing module named emoji - imported by transformers.models.bertweet.tokenization_bertweet (delayed, optional)
missing module named rhoknp - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named sudachipy - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named unidic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named unidic_lite - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named ipadic - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, conditional, optional)
missing module named fugashi - imported by transformers.models.bert_japanese.tokenization_bert_japanese (delayed, optional)
missing module named tensorflow_hub - imported by transformers.models.bert_generation.modeling_bert_generation (delayed, optional)
missing module named transformers.models.bamba.processing_bamba - imported by transformers.models.bamba (conditional)
missing module named 'dvclive.utils' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named flytekitplugins - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named flytekit - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named codecarbon - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.utils' - imported by transformers.integrations.integration_utils (delayed)
missing module named 'neptune.exceptions' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.new' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named 'neptune.internal' - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named neptune - imported by transformers.integrations.integration_utils (delayed, optional)
missing module named dagshub - imported by transformers.integrations.integration_utils (delayed)
missing module named azureml - imported by transformers.integrations.integration_utils (delayed)
missing module named 'wandb.sdk' - imported by transformers.integrations.integration_utils (delayed)
missing module named sigopt - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'datasets.load' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'ray.tune' - imported by transformers.integrations.integration_utils (delayed, conditional)
missing module named 'flute.integrations' - imported by transformers.integrations.higgs (conditional)
missing module named fast_hadamard_transform - imported by transformers.integrations.higgs (conditional)
missing module named 'flute.utils' - imported by transformers.integrations.higgs (conditional)
missing module named fbgemm_gpu - imported by transformers.integrations.fbgemm_fp8 (conditional)
missing module named 'awq.quantize' - imported by transformers.integrations.awq (delayed, conditional)
missing module named 'awq.modules' - imported by transformers.integrations.awq (delayed, conditional)
missing module named awq - imported by transformers.integrations.awq (delayed)
missing module named aqlm - imported by transformers.integrations.aqlm (delayed)
missing module named torch.nn.BCELoss - imported by torch.nn (top-level), transformers.generation.watermarking (top-level)
missing module named sklearn - imported by transformers.generation.candidate_generator (conditional)
missing module named 'sklearn.metrics' - imported by transformers.data.metrics (conditional)
missing module named markdownify - imported by transformers.agents.search (delayed, optional)
missing module named duckduckgo_search - imported by transformers.agents.search (delayed, optional)
missing module named soundfile - imported by transformers.agents.agent_types (conditional)
missing module named kenlm - imported by transformers.pipelines (delayed, conditional, optional)
missing module named transformers.MaskFormerForInstanceSegmentationOutput - imported by transformers (conditional), transformers.models.maskformer.image_processing_maskformer (conditional)
missing module named numba - imported by torch.testing._internal.common_cuda (conditional)
missing module named 'triton.language' - imported by torch.sparse._triton_ops (conditional), torch._inductor.triton_helpers (top-level)
missing module named triton - imported by torch._dynamo.logging (conditional, optional), torch._inductor.utils (delayed, optional), torch._inductor.codecache (delayed, optional), torch._inductor.coordinate_descent_tuner (conditional), torch._inductor.triton_heuristics (conditional), torch._inductor.codegen.triton (delayed), torch.sparse._triton_ops (delayed, conditional, optional), torch._inductor.kernel.mm_common (delayed), torch._inductor.kernel.mm_plus_mm (delayed), torch._inductor.triton_helpers (top-level)
missing module named 'torch._C._autograd' - imported by torch.profiler (top-level), torch.profiler._memory_profiler (top-level), torch.autograd (top-level)
missing module named torch.nn.Module - imported by torch.nn (top-level), torch.jit._recursive (top-level), torch.jit._script (top-level), torch.jit._trace (top-level), torch.distributed.nn.api.remote_module (top-level), torch.optim.swa_utils (top-level), torch._dynamo.mutation_guard (top-level), torch.fx.passes.utils.common (top-level), torch.ao.quantization.fake_quantize (top-level), torch._dynamo.backends.cudagraphs (top-level)
missing module named 'torch._C._onnx' - imported by torch.onnx (top-level), torch.onnx.symbolic_helper (top-level), torch.onnx._globals (top-level), torch.onnx.symbolic_opset9 (top-level), torch.onnx.symbolic_opset10 (top-level), torch.onnx.utils (top-level), torch.onnx.symbolic_opset13 (top-level), torch.onnx._experimental (top-level), torch.onnx.verification (top-level)
missing module named 'onnxruntime.capi' - imported by torch.onnx._internal.onnxruntime (optional)
missing module named 'onnx.defs' - imported by torch.onnx._internal.fx.type_utils (delayed, conditional)
missing module named onnxscript - imported by torch.onnx._internal.fx.registration (conditional), torch.onnx._internal.exporter (delayed, conditional, optional), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (conditional), torch.onnx._internal.fx.fx_onnx_interpreter (top-level), torch.onnx._internal.fx.op_validation (top-level)
missing module named 'onnxscript.function_libs' - imported by torch.onnx._internal.exporter (delayed, conditional), torch.onnx._internal.fx.diagnostics (top-level), torch.onnx._internal.fx.onnxfunction_dispatcher (top-level), torch.onnx._internal.fx.fx_onnx_interpreter (top-level)
missing module named beartype - imported by torch.onnx._internal._beartype (optional)
missing module named torch.multiprocessing._prctl_pr_set_pdeathsig - imported by torch.multiprocessing (top-level), torch.multiprocessing.spawn (top-level)
missing module named 'torch._C._monitor' - imported by torch.monitor (top-level)
missing module named astunparse - imported by torch.jit.frontend (optional), torch._dynamo.guards (conditional, optional)
missing module named 'torch._C._jit_tree_views' - imported by torch._sources (top-level), torch.jit.frontend (top-level)
missing module named torch.TensorType - imported by torch (top-level), torch.jit._passes._property_propagation (top-level)
missing module named 'monkeytype.tracing' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.db' - imported by torch.jit._monkeytype_config (optional)
missing module named 'monkeytype.config' - imported by torch.jit._monkeytype_config (optional)
missing module named monkeytype - imported by torch.jit._monkeytype_config (optional)
missing module named pydot - imported by networkx.drawing.nx_pydot (delayed), torch.fx.passes.graph_drawer (optional)
missing module named z3 - imported by torch.fx.experimental.validator (optional), torch.fx.experimental.migrate_gradual_types.transform_to_z3 (optional), torch.fx.experimental.migrate_gradual_types.z3_types (optional)
missing module named 'torch._C._distributed_rpc' - imported by torch.distributed.rpc (conditional), torch.distributed.rpc.api (top-level), torch.distributed.rpc.internal (top-level), torch.distributed.rpc.constants (top-level), torch.distributed.rpc.options (top-level), torch._jit_internal (conditional)
missing module named 'torch._C._distributed_rpc_testing' - imported by torch.distributed.rpc._testing (conditional)
missing module named torch.cuda.FloatTensor - imported by torch.cuda (top-level), torch.distributed.fsdp.sharded_grad_scaler (top-level)
missing module named torchdistx - imported by torch.distributed.fsdp._init_utils (optional)
missing module named etcd - imported by torch.distributed.elastic.rendezvous.etcd_rendezvous (top-level), torch.distributed.elastic.rendezvous.etcd_store (top-level), torch.distributed.elastic.rendezvous.etcd_rendezvous_backend (top-level), torch.distributed.elastic.rendezvous.etcd_server (optional)
missing module named 'torch.distributed.elastic.metrics.static_init' - imported by torch.distributed.elastic.metrics (optional)
missing module named 'torch._C._distributed_autograd' - imported by torch.distributed.autograd (conditional)
missing module named pickle5 - imported by numpy.compat.py3k (optional)
missing module named _dummy_thread - imported by numpy.core.arrayprint (optional), cffi.lock (conditional, optional), torch._jit_internal (optional), future.backports.misc (conditional, optional)
missing module named numpy.eye - imported by numpy (delayed), numpy.core.numeric (delayed)
missing module named numpy.core.integer - imported by numpy.core (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.conjugate - imported by numpy.core (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.ufunc - imported by numpy.core (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.ones - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.hstack - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_1d - imported by numpy.core (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.atleast_3d - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.vstack - imported by numpy.core (top-level), numpy.lib.shape_base (top-level)
missing module named numpy.core.linspace - imported by numpy.core (top-level), numpy.lib.index_tricks (top-level)
missing module named numpy.core.transpose - imported by numpy.core (top-level), numpy.lib.function_base (top-level)
missing module named numpy.core.result_type - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.float_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.number - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.max - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.bool_ - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.inf - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.array2string - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.signbit - imported by numpy.core (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.core.isscalar - imported by numpy.core (delayed), numpy.testing._private.utils (delayed), numpy.lib.polynomial (top-level)
missing module named numpy.core.isnat - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.ndarray - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.lib.utils (top-level)
missing module named numpy.core.array_repr - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.arange - imported by numpy.core (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.float32 - imported by numpy.core (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.iinfo - imported by numpy.core (top-level), numpy.lib.twodim_base (top-level)
missing module named numpy.core.reciprocal - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.argsort - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sign - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.isnan - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.count_nonzero - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.divide - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.swapaxes - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.matmul - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.object_ - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.asanyarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intp - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.core.atleast_2d - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.prod - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amax - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.amin - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.moveaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.geterrobj - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.errstate - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.finfo - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.isfinite - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sum - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.sqrt - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.multiply - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.add - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.dot - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.core.Inf - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.all - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (delayed)
missing module named numpy.core.newaxis - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.complexfloating - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.inexact - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.cdouble - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.csingle - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.double - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.single - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.intc - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty_like - imported by numpy.core (top-level), numpy.linalg.linalg (top-level)
missing module named numpy.core.empty - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.zeros - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.fft._pocketfft (top-level)
missing module named numpy.core.asarray - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.lib.utils (top-level), numpy.fft._pocketfft (top-level), numpy.fft.helper (top-level)
missing module named numpy.core.array - imported by numpy.core (top-level), numpy.linalg.linalg (top-level), numpy.testing._private.utils (top-level), numpy.lib.polynomial (top-level)
missing module named numpy.array - imported by numpy (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.dtype - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.array_api._typing (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level)
missing module named numpy.bool_ - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.recarray - imported by numpy (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level)
missing module named numpy.ndarray - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ma.core (top-level), numpy.ma.extras (top-level), numpy.lib.recfunctions (top-level), numpy.ma.mrecords (top-level), numpy.ctypeslib (top-level), imageio.typing (optional)
missing module named numpy.histogramdd - imported by numpy (delayed), numpy.lib.twodim_base (delayed)
missing module named numpy.expand_dims - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.iscomplexobj - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amin - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named numpy.amax - imported by numpy (top-level), numpy.ma.core (top-level)
missing module named threadpoolctl - imported by numpy.lib.utils (delayed, optional)
missing module named numpy.lib.imag - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.real - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.lib.iscomplexobj - imported by numpy.lib (delayed), numpy.testing._private.utils (delayed)
missing module named numpy.ufunc - imported by numpy (top-level), numpy._typing (top-level), numpy.testing.overrides (top-level), skimage._vendored.numpy_lookfor (top-level)
missing module named numpy.isinf - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.isnan - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named numpy.isfinite - imported by numpy (top-level), numpy.testing._private.utils (top-level)
missing module named 'unittest.case' - imported by numpy.testing._private.utils (top-level)
missing module named numpy.float64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.float32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint16 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.uint8 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int64 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int32 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int16 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.int8 - imported by numpy (top-level), numpy.array_api._typing (top-level)
missing module named numpy.bytes_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.str_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.void - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.object_ - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.datetime64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.timedelta64 - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.number - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.complexfloating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.floating - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.integer - imported by numpy (top-level), numpy._typing._array_like (top-level), numpy.ctypeslib (top-level)
missing module named numpy.unsignedinteger - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy.generic - imported by numpy (top-level), numpy._typing._array_like (top-level)
missing module named numpy._typing._ufunc - imported by numpy._typing (conditional)
missing module named 'tensorflow.core' - imported by torch.contrib._tensorboard_vis (optional)
missing module named opt_einsum - imported by torch.backends.opt_einsum (optional)
missing module named 'coremltools.models' - imported by torch.backends._coreml.preprocess (top-level)
missing module named 'coremltools.converters' - imported by torch.backends._coreml.preprocess (top-level)
missing module named coremltools - imported by torch.backends._coreml.preprocess (top-level)
missing module named pytorch_lightning - imported by torch.ao.pruning._experimental.data_sparsifier.lightning.callbacks.data_sparsity (top-level)
missing module named torch.nn.BatchNorm3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.BatchNorm1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Linear - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.ReLU - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv3d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv2d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named torch.nn.Conv1d - imported by torch.nn (top-level), torch.ao.nn.intrinsic.modules.fused (top-level)
missing module named 'torch._C._functorch' - imported by torch._functorch.pyfunctorch (top-level), torch._functorch.vmap (top-level), torch._functorch.utils (top-level), torch._subclasses.meta_utils (top-level), torch._functorch.autograd_function (top-level), torch._functorch.eager_transforms (top-level)
missing module named torch._numpy.float_ - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.any - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.max - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isnan - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.all - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.signbit - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.real - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.isscalar - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.iscomplexobj - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.imag - imported by torch._numpy (delayed), torch._numpy.testing.utils (delayed)
missing module named torch._numpy.intp - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.empty - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named torch._numpy.arange - imported by torch._numpy (top-level), torch._numpy.testing.utils (top-level)
missing module named 'torch._C._lazy_ts_backend' - imported by torch._lazy.ts_backend (top-level), torch._lazy.computation (top-level)
missing module named 'torch._C._lazy' - imported by torch._lazy (top-level), torch._lazy.device_context (top-level), torch._lazy.metrics (top-level), torch._lazy.computation (top-level), torch._lazy.config (top-level), torch._lazy.debug (top-level), torch._lazy.ir_cache (top-level)
missing module named 'triton.testing' - imported by torch._inductor.utils (delayed, optional)
missing module named 'triton.runtime' - imported by torch._inductor.triton_heuristics (conditional), torch._inductor.codegen.triton_utils (delayed)
missing module named torch._inductor.fx_passes.fb - imported by torch._inductor.fx_passes.pre_grad (delayed, conditional)
missing module named deeplearning - imported by torch._inductor.fx_passes.group_batch_fusion (optional)
missing module named 'torch._inductor.fb' - imported by torch._inductor.codecache (conditional), torch._inductor.compile_fx (conditional)
missing module named 'triton.fb' - imported by torch._inductor.codecache (conditional)
missing module named pygraphviz - imported by networkx.drawing.nx_agraph (delayed, optional)
missing module named 'matplotlib.cm' - imported by networkx.drawing.nx_pylab (delayed)
missing module named 'matplotlib.path' - imported by networkx.drawing.nx_pylab (delayed)
missing module named foo - imported by torch._functorch.compilers (delayed)
missing module named 'torchrec.sparse' - imported by torch._dynamo.variables.user_defined (delayed)
missing module named torchrec - imported by torch._dynamo.variables.user_defined (delayed, optional)
missing module named 'torch._C._dynamo' - imported by torch._dynamo.types (conditional), torch._dynamo.decorators (conditional), torch._dynamo.eval_frame (conditional)
missing module named 'torch.fb' - imported by torch._dynamo.exc (conditional)
missing module named libfb - imported by torch._dynamo.debug_utils (conditional)
missing module named 'tvm.contrib' - imported by torch._dynamo.backends.tvm (delayed)
missing module named tvm - imported by torch._dynamo.backends.tvm (delayed, conditional)
missing module named 'libfb.py' - imported by torch._inductor.config (conditional)
missing module named 'torch._C._VariableFunctions' - imported by torch (conditional)
missing module named torch.trunc - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.tan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.square - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sqrt - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.signbit - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.sign - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.round - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.reciprocal - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.rad2deg - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.negative - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.logical_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log1p - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log10 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.log - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isnan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isinf - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.isfinite - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.floor - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.expm1 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp2 - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.exp - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.deg2rad - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.cos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.conj_physical - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.ceil - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.bitwise_not - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctanh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arctan - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsinh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arcsin - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccosh - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.arccos - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.absolute - imported by torch (top-level), torch._numpy._unary_ufuncs_impl (top-level)
missing module named torch.true_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.subtract - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.remainder - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.pow - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.not_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.nextafter - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.multiply - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.minimum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.maximum - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logical_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.logaddexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.less - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.ldexp - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.lcm - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.hypot - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.heaviside - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater_equal - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.greater - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.gcd - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmod - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmin - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.fmax - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.floor_divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.float_power - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.eq - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.divide - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.copysign - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_xor - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_right_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_or - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_left_shift - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.bitwise_and - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.arctan2 - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.add - imported by torch (top-level), torch._numpy._binary_ufuncs_impl (top-level)
missing module named torch.tensor - imported by torch (top-level), transformers.models.deprecated.ernie_m.modeling_ernie_m (top-level), torch.utils.benchmark.utils.compare (top-level)
missing module named torch.Size - imported by torch (top-level), transformers.models.nemotron.modeling_nemotron (top-level), torch.nn.modules.normalization (top-level)
missing module named torch.Generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.randperm - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch.default_generator - imported by torch (top-level), torch.utils.data.dataset (top-level)
missing module named torch._softmax_backward_data - imported by torch (delayed), transformers.pytorch_utils (delayed)
missing module named torch.broadcast_shapes - imported by torch (top-level), torch._numpy._funcs_impl (top-level)
missing module named torch.norm_except_dim - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch._weight_norm - imported by torch (top-level), torch.nn.utils.weight_norm (top-level)
missing module named torch.device - imported by torch (top-level), torch.nn.modules.module (top-level), torch.cuda (top-level), torch.distributed.nn.api.remote_module (top-level), transformers.models.blip.modeling_blip_text (top-level), torch.cpu (top-level)
missing module named graphviz - imported by ffmpeg._view (delayed, optional)
missing module named __builtin__ - imported by future.builtins.misc (conditional), future.builtins.new_min_max (conditional), future.utils (conditional), past.types (conditional), past.builtins.noniterators (conditional), past.builtins (conditional), past.builtins.misc (conditional)
missing module named _dbm - imported by dbm.ndbm (top-level)
missing module named gdbm - imported by anydbm (top-level), future.moves.dbm.gnu (conditional)
missing module named _gdbm - imported by dbm.gnu (top-level)
missing module named dumbdbm - imported by anydbm (top-level), future.moves.dbm.dumb (conditional)
missing module named anydbm - imported by future.moves.dbm (conditional)
missing module named dbhash - imported by anydbm (top-level)
missing module named whichdb - imported by future.moves.dbm (conditional), anydbm (top-level)
missing module named 'test.test_support' - imported by future.moves.test.support (conditional)
missing module named 'test.support' - imported by future.moves.test.support (conditional)
missing module named dummy_thread - imported by cffi.lock (conditional, optional), future.backports.misc (conditional, optional)
missing module named thread - imported by cffi.lock (conditional, optional), cffi.cparser (conditional, optional), future.backports.misc (conditional, optional)
missing module named future_builtins - imported by future.builtins.misc (conditional)
missing module named defusedxml - imported by PIL.Image (optional), tifffile.tifffile (delayed, optional)
missing module named zarr - imported by tifffile.tifffile (delayed)
missing module named _imagecodecs - imported by tifffile.tifffile (delayed, conditional, optional)
missing module named imagecodecs - imported by imageio.plugins._tifffile (delayed, conditional, optional), tifffile.tifffile (optional)
missing module named SimpleITK - imported by imageio.plugins.simpleitk (delayed, optional), skimage.io._plugins.simpleitk_plugin (optional)
missing module named itk - imported by imageio.plugins.simpleitk (delayed, optional)
missing module named rawpy - imported by imageio.plugins.rawpy (top-level)
missing module named 'av.codec' - imported by imageio.plugins.pyav (top-level)
missing module named 'av.filter' - imported by imageio.plugins.pyav (top-level)
missing module named pillow_heif - imported by imageio.plugins.pillow (delayed, optional)
missing module named osgeo - imported by imageio.plugins.gdal (delayed, optional)
missing module named astropy - imported by imageio.plugins.fits (delayed, optional)
missing module named tkFileDialog - imported by imageio.plugins._tifffile (delayed, optional)
missing module named Tkinter - imported by imageio.plugins._tifffile (delayed, optional)
missing module named tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named imageio.plugins.tifffile_geodb - imported by imageio.plugins._tifffile (delayed, optional)
missing module named zstd - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named 'backports.lzma' - imported by imageio.plugins._tifffile (delayed, conditional, optional)
missing module named bsdf_cli - imported by imageio.plugins._bsdf (conditional)
missing module named pygame - imported by moviepy.video.io.preview (top-level), moviepy.audio.io.preview (top-level), moviepy.video.tools.tracking (delayed)
missing module named 'matplotlib.widgets' - imported by moviepy.video.io.sliders (top-level)
missing module named 'scipy.misc' - imported by moviepy.video.fx.resize (optional)
missing module named 'skimage.filter' - imported by moviepy.video.fx.painting (optional)
missing module named PIL._imagingagg - imported by PIL (delayed, conditional, optional), PIL.ImageDraw (delayed, conditional, optional)
missing module named olefile - imported by PIL.FpxImagePlugin (top-level), PIL.MicImagePlugin (top-level)
missing module named cStringIO - imported by cffi.ffiplatform (optional)
missing module named cPickle - imported by pycparser.ply.yacc (delayed, optional)
missing module named cffi._pycparser - imported by cffi (optional), cffi.cparser (optional)
missing module named numpy._distributor_init_local - imported by numpy (optional), numpy._distributor_init (optional)
missing module named ttkbootstrap.dialogs.Dialog - imported by ttkbootstrap.dialogs (top-level), ttkbootstrap.dialogs.colorchooser (top-level)
