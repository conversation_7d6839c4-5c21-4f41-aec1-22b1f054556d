import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import os
from threading import Thread
import sys
from moviepy.editor import VideoFileClip
import ttkbootstrap as ttk
from ttkbootstrap.constants import *

# 检查必要的依赖
def check_dependencies():
    missing_deps = []
    try:
        import moviepy.editor
    except ImportError:
        missing_deps.append("moviepy==1.0.3")
    
    if missing_deps:
        msg = "缺少必要的依赖库，请使用以下命令安装：\n\n"
        msg += "pip install " + " ".join(missing_deps)
        messagebox.showerror("依赖错误", msg)
        return False
    return True

class VideoWatermark:
    def __init__(self, parent=None):
        # 检查依赖
        if not check_dependencies():
            sys.exit(1)
        if parent:
            # 如果有父窗口，使用 Toplevel
            self.root = ttk.Toplevel(parent)
            self.root.title("视频水印添加工具")
            self.root.geometry("800x650")
        else:
            # 独立运行时使用 Window
            self.root = ttk.Window(
                title="视频水印添加工具",
                themename="darkly",
                size=(800, 650)
            )

        
        # 创建变量
        self.video_path = tk.StringVar()
        self.watermark_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.speed = tk.IntVar(value=2)
        self.watermark_width = tk.IntVar(value=100)
        self.watermark_height = tk.IntVar(value=100)
        self.transparent = tk.DoubleVar(value=0.5)
        
        self.setup_ui()
        
    def setup_ui(self):
        # 设置主题颜色
        style = ttk.Style()
        style.configure("TFrame", background="#f0f5ff")
        style.configure("TLabelframe", background="#f0f5ff")
        style.configure("TLabelframe.Label", background="#f0f5ff", foreground="#1a56db", font=("微软雅黑", 10, "bold"))
        style.configure("TLabel", background="#f0f5ff", foreground="#2563eb", font=("微软雅黑", 9))
        style.configure("TScale", background="#f0f5ff")
        style.configure("TEntry", padding=5)
        
        # 设置窗口
        self.root.configure(bg="#f0f5ff")
        self.root.title("视频水印添加工具 v1.0")
        self.root.geometry("800x650")  # 增加窗口尺寸
        
        # 主容器
        main_frame = ttk.Frame(self.root, padding="20 20 20 20")
        main_frame.pack(fill="both", expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, 
                               text="视频水印处理工具", 
                               font=("微软雅黑", 16, "bold"),
                               foreground="#1e40af")
        title_label.pack(pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="15")
        file_frame.pack(fill="x", padx=5, pady=5)
        
        # 视频选择
        video_frame = ttk.Frame(file_frame)
        video_frame.pack(fill="x", pady=5)
        ttk.Label(video_frame, text="选择视频:").pack(side="left")
        ttk.Entry(video_frame, textvariable=self.video_path).pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(video_frame, text="浏览", command=self.select_video, style="primary.TButton").pack(side="left")
        
        # 水印选择
        watermark_frame = ttk.Frame(file_frame)
        watermark_frame.pack(fill="x", pady=5)
        ttk.Label(watermark_frame, text="选择水印:").pack(side="left")
        ttk.Entry(watermark_frame, textvariable=self.watermark_path).pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(watermark_frame, text="浏览", command=self.select_watermark, style="primary.TButton").pack(side="left")
        
        # 输出路径选择
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill="x", pady=5)
        ttk.Label(output_frame, text="输出位置:").pack(side="left")
        ttk.Entry(output_frame, textvariable=self.output_path).pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(output_frame, text="浏览", command=self.select_output, style="primary.TButton").pack(side="left")
        
        # 参数设置区域
        param_frame = ttk.LabelFrame(main_frame, text="参数设置", padding="15")
        param_frame.pack(fill="x", padx=5, pady=15)
        
        # 水印大小设置
        size_frame = ttk.Frame(param_frame)
        size_frame.pack(fill="x", pady=5)
        ttk.Label(size_frame, text="水印尺寸:").pack(side="left")
        ttk.Entry(size_frame, textvariable=self.watermark_width, width=8).pack(side="left", padx=(5,0))
        ttk.Label(size_frame, text="×").pack(side="left", padx=2)
        ttk.Entry(size_frame, textvariable=self.watermark_height, width=8).pack(side="left")
        
        # 速度设置
        speed_frame = ttk.Frame(param_frame)
        speed_frame.pack(fill="x", pady=10)
        ttk.Label(speed_frame, text="移动速度:").pack(side="left")
        ttk.Scale(speed_frame, from_=1, to=10, variable=self.speed, orient="horizontal").pack(side="left", fill="x", expand=True, padx=5)
        
        # 透明度设置
        trans_frame = ttk.Frame(param_frame)
        trans_frame.pack(fill="x", pady=5)
        ttk.Label(trans_frame, text="透明度:  ").pack(side="left")
        ttk.Scale(trans_frame, from_=0.1, to=1.0, variable=self.transparent, orient="horizontal").pack(side="left", fill="x", expand=True, padx=5)
        
        # 开始按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(pady=20)
        start_btn = ttk.Button(
            btn_frame, 
            text="开始处理", 
            command=self.start_process, 
            style="success.TButton",
            width=20
        )
        start_btn.pack(pady=10)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(
            self.root, 
            textvariable=self.status_var,
            relief="sunken",
            background="#e0e7ff",
            font=("微软雅黑", 9)
        )
        self.status_label.pack(side="bottom", fill="x", padx=10, pady=5)

    def select_video(self):
        filename = filedialog.askopenfilename(filetypes=[("视频文件", "*.mp4")])
        if filename:
            self.video_path.set(filename)

    def select_watermark(self):
        filename = filedialog.askopenfilename(filetypes=[("PNG文件", "*.png")])
        if filename:
            self.watermark_path.set(filename)

    def select_output(self):
        folder = filedialog.askdirectory()
        if folder:
            self.output_path.set(folder)

    def add_watermark(self):
        try:
            # 检查文件是否存在
            if not os.path.exists(self.video_path.get()):
                raise FileNotFoundError("视频文件不存在")
            if not os.path.exists(self.watermark_path.get()):
                raise FileNotFoundError("水印图片不存在")
            
            # 创建临时文件路径用于无声视频
            temp_video_path = os.path.join(self.output_path.get(), "temp_video.mp4")
            final_output_path = os.path.join(self.output_path.get(), "watermarked_" + os.path.basename(self.video_path.get()))
            
            video = cv2.VideoCapture(self.video_path.get())
            if not video.isOpened():
                raise Exception("无法打开视频文件")
            
            # 读取水印图片并检查
            watermark = cv2.imread(self.watermark_path.get(), cv2.IMREAD_UNCHANGED)
            if watermark is None:
                raise Exception("无法读取水印图片，请确保图片格式正确且包含透明通道")
            
            # 检查水印是否包含透明通道
            if watermark.shape[2] != 4:
                raise Exception("水印图片必须包含透明通道(PNG格式)")
            
            # 检查并调整水印大小
            width = self.watermark_width.get()
            height = self.watermark_height.get()
            if width <= 0 or height <= 0:
                raise ValueError("水印尺寸必须大于0")
            
            watermark = cv2.resize(watermark, (width, height))
            
            # 设置输出视频
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            fps = video.get(cv2.CAP_PROP_FPS)
            frame_width = int(video.get(cv2.CAP_PROP_FRAME_WIDTH))
            frame_height = int(video.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # 检查输出路径
            output_dir = self.output_path.get()
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 修改输出路径为临时文件
            output_video = cv2.VideoWriter(
                temp_video_path, 
                fourcc, 
                fps, 
                (frame_width, frame_height), 
                isColor=True
            )
            
            if not output_video.isOpened():
                raise Exception("无法创建输出视频文件")
            
            # 初始化水印位置和速度
            x_offset, y_offset = 20, 20
            x_speed = y_speed = self.speed.get()
            
            frame_count = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
            current_frame = 0
            
            while True:
                ret, frame = video.read()
                if not ret:
                    break
                
                current_frame += 1
                self.status_var.set(f"处理进度: {current_frame}/{frame_count}")
                self.root.update()
                
                # 更新水印位置
                y_offset += y_speed
                x_offset += x_speed
                
                # 边界检查
                if y_offset + watermark.shape[0] > frame_height or y_offset < 0:
                    y_speed *= -1
                    y_offset = max(0, min(y_offset, frame_height - watermark.shape[0]))
                if x_offset + watermark.shape[1] > frame_width or x_offset < 0:
                    x_speed *= -1
                    x_offset = max(0, min(x_offset, frame_width - watermark.shape[1]))
                
                # 添加水印
                y1, y2 = int(y_offset), int(y_offset + watermark.shape[0])
                x1, x2 = int(x_offset), int(x_offset + watermark.shape[1])
                
                # 确保坐标在有效范围内
                if (y1 >= 0 and y2 <= frame_height and 
                    x1 >= 0 and x2 <= frame_width):
                    
                    alpha_s = (watermark[:, :, 3] / 255.0) * self.transparent.get()
                    alpha_l = 1.0 - alpha_s
                    
                    for c in range(0, 3):
                        frame[y1:y2, x1:x2, c] = (
                            alpha_s * watermark[:, :, c] +
                            alpha_l * frame[y1:y2, x1:x2, c]
                        )
                
                output_video.write(frame)
            
            # 释放资源
            video.release()
            output_video.release()
            cv2.destroyAllWindows()
            
            # 使用moviepy合并视频和音频
            self.status_var.set("正在合并音频...")
            self.root.update()
            
            try:

                # 读取原始视频的音频
                original_video = VideoFileClip(self.video_path.get())
                # 读取添加了水印的视频
                watermarked_video = VideoFileClip(temp_video_path)
                # 设置音频
                final_video = watermarked_video.set_audio(original_video.audio)
                # 保存最终视频
                final_video.write_videofile(
                    final_output_path,
                    codec='libx264',
                    audio_codec='aac',
                    temp_audiofile='temp-audio.m4a',
                    remove_temp=True
                )
                # 关闭视频文件
                original_video.close()
                watermarked_video.close()
                final_video.close()
                
                # 删除临时文件
                if os.path.exists(temp_video_path):
                    os.remove(temp_video_path)
                
                self.status_var.set("处理完成！")
                messagebox.showinfo("完成", "视频水印添加完成！")
                
            except Exception as e:
                if os.path.exists(temp_video_path):
                    os.remove(temp_video_path)
                raise Exception(f"合并音频时出错: {str(e)}")
            
        except FileNotFoundError as e:
            messagebox.showerror("错误", str(e))
            self.status_var.set("处理失败：文件不存在")
        except Exception as e:
            messagebox.showerror("错误", f"处理过程中出现错误：{str(e)}")
            self.status_var.set("处理失败")

    def start_process(self):
        if not self.video_path.get() or not self.watermark_path.get() or not self.output_path.get():
            messagebox.showwarning("警告", "请选择所有必要的文件和路径")
            return
        
        Thread(target=self.add_watermark, daemon=True).start()

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = VideoWatermark()
    app.run() 