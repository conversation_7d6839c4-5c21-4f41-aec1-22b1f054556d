# -*- coding: utf-8 -*-
"""
PyInstaller hook for OpenCV (cv2)
解决 OpenCV 导入问题
"""

from PyInstaller.utils.hooks import collect_submodules, collect_data_files, collect_dynamic_libs

# 收集所有 cv2 子模块
hiddenimports = collect_submodules('cv2')

# 添加特定的隐藏导入
hiddenimports += [
    'cv2.cv2',
    'numpy',
    'numpy.core._multiarray_tests',
]

# 收集动态库
binaries = collect_dynamic_libs('cv2')

# 收集数据文件
datas = collect_data_files('cv2')

# 排除测试模块
excludedimports = [
    'cv2.tests',
]
