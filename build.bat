@echo off
chcp 65001 >nul
echo 视频水印工具打包脚本
echo ========================

REM 检查 Python 是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到 Python，请先安装 Python
    pause
    exit /b 1
)

REM 检查 PyInstaller 是否安装
python -c "import PyInstaller" >nul 2>&1
if %errorlevel% neq 0 (
    echo 安装 PyInstaller...
    pip install pyinstaller
)

REM 清理之前的构建
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist

echo 开始打包...

REM 方法1: 使用 spec 文件
echo 尝试使用 spec 文件打包...
pyinstaller --clean --noconfirm video_watermark_tool.spec

REM 检查是否成功
if exist "dist\视频水印工具.exe" (
    echo 打包成功！
    goto :copy_files
)

REM 方法2: 简单打包
echo spec 文件打包失败，尝试简单打包...
pyinstaller --onefile --windowed --name="视频水印工具" ^
    --add-data="config.ini;." ^
    --hidden-import=moviepy ^
    --hidden-import=cv2 ^
    --hidden-import=ttkbootstrap ^
    --hidden-import=PIL ^
    --hidden-import=numpy ^
    --hidden-import=tqdm ^
    --hidden-import=wmi ^
    --hidden-import=configparser ^
    main.py

:copy_files
REM 复制配置文件
if exist config.ini copy config.ini dist\
if exist "优化总结.md" copy "优化总结.md" dist\

echo.
echo 打包完成！
echo 可执行文件位置: dist\视频水印工具.exe
echo.

REM 测试运行
set /p test="是否测试运行？(y/n): "
if /i "%test%"=="y" (
    cd dist
    start "视频水印工具.exe"
    cd ..
)

pause
