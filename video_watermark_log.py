﻿import tkinter as tk\nimport logging\nimport traceback
from tkinter import ttk, filedialog, messagebox
import cv2
import os
from threading import Thread
import sys
from moviepy.editor import VideoFileClip
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import time
from moviepy.config import change_settings
from concurrent.futures import Thread<PERSON>oolExecutor, wait
import asyncio
from functools import partial
import numpy as np
from PIL import ImageFont, Image, ImageDraw

# 妫€鏌ュ繀瑕佺殑渚濊禆
def check_dependencies():
    missing_deps = []
    try:
        import moviepy.editor
    except ImportError:
        missing_deps.append("moviepy==1.0.3")
    
    if missing_deps:
        msg = "缂哄皯蹇呰鐨勪緷璧栧簱锛岃浣跨敤浠ヤ笅鍛戒护瀹夎锛歕n\n"
        msg += "pip install " + " ".join(missing_deps)
        messagebox.showerror("渚濊禆閿欒", msg)
        return False
    return True

class VideoWatermark:
    def __init__(self, parent=None):  # 娣诲姞 parent 鍙傛暟
        # 妫€鏌ヤ緷璧?        if not check_dependencies():
            sys.exit(1)
        
        if parent:
            # 濡傛灉鏈夌埗绐楀彛锛屼娇鐢?Toplevel
            self.root = ttk.Toplevel(parent)
            self.root.title("瑙嗛姘村嵃娣诲姞宸ュ叿")
            self.root.geometry("800x900")
        else:
            # 鐙珛杩愯鏃朵娇鐢?Window
            self.root = ttk.Window(
                title="瑙嗛姘村嵃娣诲姞宸ュ叿",
                themename="darkly",
                size=(800, 900)
            )
        
        # 鍒涘缓鍙橀噺骞惰缃粯璁ゅ€?        self.video_path = tk.StringVar()
        self.watermark_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.speed = tk.IntVar()
        self.watermark_width = tk.IntVar()
        self.watermark_height = tk.IntVar()
        self.transparent = tk.DoubleVar()
        self.font_size = tk.IntVar()
        self.text_color = tk.StringVar()
        self.text_transparent = tk.DoubleVar()
        self.text_direction = tk.StringVar()
        self.text_position = tk.StringVar()
        
        # 璁剧疆榛樿鍊?        self.speed.set(3)
        self.watermark_width.set(200)
        self.watermark_height.set(200)
        self.transparent.set(0.3)
        self.font_size.set(20)
        self.text_color.set("#FFFFFF")
        self.text_transparent.set(0.9)
        self.text_direction.set("horizontal")
        self.text_position.set("bottom-center")
        
        self.setup_ui()
        
        # 璁剧疆榛樿鏂囨湰姘村嵃鍐呭
        default_text = "娲诲姩浼樻儬璇︽儏浠ュ疄闄呭埌搴楃粨鏋滀负涓籠n鑺滄箹涓鍙ｈ厰鍖婚櫌\n锛堣姕锛夊尰骞裤€?024銆戠07-03-60鍙?
        self.text_input.insert("1.0", default_text)

    def setup_ui(self):
        # 绉婚櫎鑷畾涔夋牱寮忚缃紝浣跨敤 ttkbootstrap 鐨勪富棰?        
        # 涓诲鍣?        main_frame = ttk.Frame(self.root, padding="20 20 20 20")
        main_frame.pack(fill="both", expand=True)
        
        # 鏍囬
        title_label = ttk.Label(
            main_frame, 
            text="瑙嗛姘村嵃澶勭悊宸ュ叿", 
            font=("寰蒋闆呴粦", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 鍒涘缓 Notebook 鐢ㄤ簬鍒囨崲鍥剧墖姘村嵃鍜屾枃鏈按鍗?        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill="x", padx=5, pady=5)
        
        # 鍥剧墖姘村嵃鏍囩椤?        image_frame = ttk.Frame(notebook)
        notebook.add(image_frame, text="鍥剧墖姘村嵃")
        
        # 鏂囨湰姘村嵃鏍囩椤?        text_frame = ttk.Frame(notebook)
        notebook.add(text_frame, text="鏂囨湰姘村嵃")
        
        # 娣诲姞鍘婚櫎姘村嵃鏍囩椤?        remove_frame = ttk.Frame(notebook)
        notebook.add(remove_frame, text="鍘婚櫎姘村嵃")
        
        # === 鍥剧墖姘村嵃璁剧疆 ===
        # 姘村嵃閫夋嫨
        watermark_frame = ttk.Frame(image_frame)
        watermark_frame.pack(fill="x", pady=5)
        ttk.Label(watermark_frame, text="閫夋嫨姘村嵃:").pack(side="left")
        ttk.Entry(watermark_frame, textvariable=self.watermark_path).pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(watermark_frame, text="娴忚", command=self.select_watermark, bootstyle="info-outline").pack(side="left")
        
        # 鍥剧墖姘村嵃鍙傛暟璁剧疆
        img_param_frame = ttk.LabelFrame(image_frame, text="鍥剧墖姘村嵃璁剧疆", padding="15")
        img_param_frame.pack(fill="x", padx=5, pady=5)
        
        # 姘村嵃澶у皬璁剧疆
        size_frame = ttk.Frame(img_param_frame)
        size_frame.pack(fill="x", pady=5)
        ttk.Label(size_frame, text="姘村嵃灏哄:").pack(side="left")
        ttk.Entry(size_frame, textvariable=self.watermark_width, width=8).pack(side="left", padx=(5,0))
        ttk.Label(size_frame, text="脳").pack(side="left", padx=2)
        ttk.Entry(size_frame, textvariable=self.watermark_height, width=8).pack(side="left")
        
        # 閫熷害璁剧疆
        speed_frame = ttk.Frame(img_param_frame)
        speed_frame.pack(fill="x", pady=10)
        ttk.Label(speed_frame, text="绉诲姩閫熷害:").pack(side="left")
        ttk.Scale(speed_frame, from_=1, to=10, variable=self.speed, orient="horizontal").pack(side="left", fill="x", expand=True, padx=5)
        
        # 閫忔槑搴﹁缃?        trans_frame = ttk.Frame(img_param_frame)
        trans_frame.pack(fill="x", pady=5)
        ttk.Label(trans_frame, text="閫忔槑搴?  ").pack(side="left")
        ttk.Scale(trans_frame, from_=0.1, to=1.0, variable=self.transparent, orient="horizontal").pack(side="left", fill="x", expand=True, padx=5)
        
        # === 鏂囨湰姘村嵃璁剧疆 ===
        # 鏂囨湰杈撳叆
        text_input_frame = ttk.Frame(text_frame)
        text_input_frame.pack(fill="both", expand=True, pady=5)
        ttk.Label(text_input_frame, text="姘村嵃鏂囨湰:").pack(side="top", anchor="w")
        self.text_input = tk.Text(text_input_frame, height=4, width=40)
        self.text_input.pack(fill="both", expand=True, pady=5)
        
        # 鏂囨湰鍙傛暟璁剧疆
        text_param_frame = ttk.LabelFrame(text_frame, text="鏂囨湰璁剧疆", padding="15")
        text_param_frame.pack(fill="x", padx=5, pady=5)
        
        # 鏂囧瓧澶у皬
        font_size_frame = ttk.Frame(text_param_frame)
        font_size_frame.pack(fill="x", pady=5)
        ttk.Label(font_size_frame, text="瀛椾綋澶у皬:").pack(side="left")
        ttk.Entry(font_size_frame, textvariable=self.font_size, width=8).pack(side="left", padx=5)
        
        # 鏂囧瓧棰滆壊
        color_frame = ttk.Frame(text_param_frame)
        color_frame.pack(fill="x", pady=5)
        ttk.Label(color_frame, text="鏂囧瓧棰滆壊:").pack(side="left")
        ttk.Entry(color_frame, textvariable=self.text_color, width=8).pack(side="left", padx=5)
        ttk.Button(color_frame, text="閫夋嫨棰滆壊", command=self.choose_color, bootstyle="info-outline").pack(side="left", padx=5)
        
        # 鏂囧瓧閫忔槑搴?        text_trans_frame = ttk.Frame(text_param_frame)
        text_trans_frame.pack(fill="x", pady=5)
        ttk.Label(text_trans_frame, text="閫忔槑搴?").pack(side="left")
        ttk.Scale(text_trans_frame, from_=0.1, to=1.0, variable=self.text_transparent, orient="horizontal").pack(side="left", fill="x", expand=True, padx=5)
        
        # 鏂囧瓧鏂瑰悜
        direction_frame = ttk.Frame(text_param_frame)
        direction_frame.pack(fill="x", pady=5)
        ttk.Label(direction_frame, text="鏂囧瓧鏂瑰悜:").pack(side="left")
        ttk.Radiobutton(direction_frame, text="姘村钩", variable=self.text_direction, value="horizontal").pack(side="left", padx=5)
        ttk.Radiobutton(direction_frame, text="鍨傜洿", variable=self.text_direction, value="vertical").pack(side="left", padx=5)
        
        # 鏂囧瓧浣嶇疆
        position_frame = ttk.LabelFrame(text_param_frame, text="鏂囧瓧浣嶇疆璁剧疆", padding="10")
        position_frame.pack(fill="x", pady=5)
        
        # 浣嶇疆閫夋嫨
        pos_select_frame = ttk.Frame(position_frame)
        pos_select_frame.pack(fill="x", pady=5)
        ttk.Label(pos_select_frame, text="浣嶇疆绫诲瀷:").pack(side="left")
        self.text_position = tk.StringVar(value="bottom-center")
        positions = [
            ("搴曢儴灞呬腑", "bottom-center"),
            ("宸︿晶灞呬腑", "left-center"),
            ("鍙充晶灞呬腑", "right-center")
        ]
        for text, value in positions:
            ttk.Radiobutton(pos_select_frame, text=text, variable=self.text_position, 
                           value=value, command=self.update_position_controls).pack(side="left", padx=5)
        
        # 浣嶇疆姣斾緥璁剧疆妗嗘灦
        self.position_ratio_frame = ttk.Frame(position_frame)
        self.position_ratio_frame.pack(fill="x", pady=5)
        
        # 楂樺害姣斾緥锛堢敤浜庡簳閮ㄥ眳涓級
        self.height_ratio_frame = ttk.Frame(self.position_ratio_frame)
        ttk.Label(self.height_ratio_frame, text="瑙嗛楂樺害 1/").pack(side="left")
        self.height_ratio = tk.IntVar(value=10)
        ttk.Entry(self.height_ratio_frame, textvariable=self.height_ratio, width=5).pack(side="left", padx=5)
        ttk.Label(self.height_ratio_frame, text="浣嶇疆").pack(side="left")
        
        # 瀹藉害姣斾緥锛堢敤浜庡簳閮ㄥ乏渚у拰鍙充晶锛?        self.width_ratio_frame = ttk.Frame(self.position_ratio_frame)
        ttk.Label(self.width_ratio_frame, text="瑙嗛瀹藉害 1/").pack(side="left")
        self.width_ratio = tk.IntVar(value=10)
        ttk.Entry(self.width_ratio_frame, textvariable=self.width_ratio, width=5).pack(side="left", padx=5)
        ttk.Label(self.width_ratio_frame, text="浣嶇疆").pack(side="left")
        
        # 鍒濆鍖栨樉绀烘纭殑鎺т欢
        self.update_position_controls()
        
        # 鏂囦欢閫夋嫨鍖哄煙
        file_frame = ttk.LabelFrame(main_frame, text="鏂囦欢閫夋嫨", padding="15")
        file_frame.pack(fill="x", padx=5, pady=5)
        
        # 瑙嗛閫夋嫨
        video_frame = ttk.Frame(file_frame)
        video_frame.pack(fill="x", pady=5)
        ttk.Label(video_frame, text="閫夋嫨瑙嗛:").pack(side="left")
        self.video_entry = ttk.Entry(video_frame, textvariable=self.video_path)
        self.video_entry.pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(video_frame, text="娴忚", command=self.select_video, bootstyle="info-outline").pack(side="left")
        
        # 娣诲姞鏂囦欢鏁伴噺鏄剧ず鏍囩
        self.file_count_label = ttk.Label(video_frame, text="")
        self.file_count_label.pack(side="left", padx=5)
        
        # 杈撳嚭璺緞閫夋嫨
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill="x", pady=5)
        ttk.Label(output_frame, text="杈撳嚭浣嶇疆:").pack(side="left")
        ttk.Entry(output_frame, textvariable=self.output_path).pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(output_frame, text="娴忚", command=self.select_output, bootstyle="info-outline").pack(side="left")
        
        # 鍙傛暟璁剧疆鍖哄煙
        param_frame = ttk.LabelFrame(main_frame, text="鍙傛暟璁剧疆", padding="15")
        param_frame.pack(fill="x", padx=5, pady=15)
        
        # 寮€濮嬫寜閽?        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(pady=20)
        start_btn = ttk.Button(
            btn_frame, 
            text="寮€濮嬪鐞?, 
            command=self.start_process, 
            bootstyle="success",
            width=20
        )
        start_btn.pack(pady=10)
        
        # 鐘舵€佹爮
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(
            self.root, 
            textvariable=self.status_var,
            relief="sunken",
            font=("寰蒋闆呴粦", 9)
        )
        self.status_label.pack(side="bottom", fill="x", padx=10, pady=5)

        # === 鍘婚櫎姘村嵃璁剧疆 ===
        # 瑙嗛閫夋嫨鍖哄煙
        remove_file_frame = ttk.Frame(remove_frame)
        remove_file_frame.pack(fill="x", pady=5)
        
        # 瑙嗛閫夋嫨
        remove_video_frame = ttk.Frame(remove_file_frame)
        remove_video_frame.pack(fill="x", pady=5)
        ttk.Label(remove_video_frame, text="閫夋嫨瑙嗛:").pack(side="left")
        self.remove_video_path = tk.StringVar()
        self.remove_video_entry = ttk.Entry(remove_video_frame, textvariable=self.remove_video_path)
        self.remove_video_entry.pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(remove_video_frame, text="娴忚", command=self.select_remove_video, bootstyle="info-outline").pack(side="left")
        
        # 娣诲姞鏂囦欢鏁伴噺鏄剧ず鏍囩
        self.remove_file_count_label = ttk.Label(remove_video_frame, text="")
        self.remove_file_count_label.pack(side="left", padx=5)
        
        # 杈撳嚭璺緞閫夋嫨
        remove_output_frame = ttk.Frame(remove_file_frame)
        remove_output_frame.pack(fill="x", pady=5)
        ttk.Label(remove_output_frame, text="杈撳嚭浣嶇疆:").pack(side="left")
        self.remove_output_path = tk.StringVar()
        ttk.Entry(remove_output_frame, textvariable=self.remove_output_path).pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(remove_output_frame, text="娴忚", command=self.select_remove_output, bootstyle="info-outline").pack(side="left")
        
        # 鎸夐挳鍖哄煙
        remove_btn_frame = ttk.Frame(remove_frame)
        remove_btn_frame.pack(pady=20)
        
        # 鍘婚櫎鏂囨湰姘村嵃鎸夐挳
        remove_text_btn = ttk.Button(
            remove_btn_frame,
            text="鍘婚櫎鏂囨湰姘村嵃",
            command=self.start_remove_text_watermark,
            bootstyle="success",
            width=20
        )
        remove_text_btn.pack(pady=5)
        
        # 杩涘害鏉?        self.remove_progress_var = tk.DoubleVar()
        self.remove_progress_bar = ttk.Progressbar(
            remove_frame,
            variable=self.remove_progress_var,
            maximum=100,
            bootstyle="success-striped"
        )
        self.remove_progress_bar.pack(fill="x", padx=10, pady=5)

    def select_video(self):
        filenames = filedialog.askopenfilenames(filetypes=[("瑙嗛鏂囦欢", "*.mp4")])
        if filenames:
            self.video_path.set(";".join(filenames))  # 浣跨敤鍒嗗彿杩炴帴澶氫釜鏂囦欢璺緞
            self.file_count_label.config(text=f"宸查€夋嫨 {len(filenames)} 涓枃浠?)

    def select_watermark(self):
        filename = filedialog.askopenfilename(filetypes=[("PNG鏂囦欢", "*.png")])
        if filename:
            self.watermark_path.set(filename)

    def select_output(self):
        folder = filedialog.askdirectory()
        if folder:
            self.output_path.set(folder)

    async def add_watermark(self):
        # 鑾峰彇鎵€鏈夎棰戞枃浠惰矾寰?        video_paths = self.video_path.get().split(";")
        total_videos = len(video_paths)
        
        # 鍒涘缓杩涘害鏄剧ず
        progress_frame = ttk.Frame(self.root)
        progress_frame.pack(side="bottom", fill="x", padx=10, pady=5)
        
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            progress_frame,
            variable=progress_var,
            maximum=total_videos,
            bootstyle="success-striped"
        )
        progress_bar.pack(fill="x")
        
        async def process_video(video_path, video_index):
            temp_video_path = None
            video = None
            output_video = None
            original_video = None
            watermarked_video = None
            final_video = None
            success = False
            
            try:
                # 鏇存柊鐘舵€?                self.status_var.set(f"姝ｅ湪澶勭悊: {os.path.basename(video_path)} ({video_index + 1}/{total_videos})")
                self.root.update()
                
                # 妫€鏌ユ枃浠舵槸鍚﹀瓨鍦?                if not os.path.exists(video_path):
                    raise FileNotFoundError("瑙嗛鏂囦欢涓嶅瓨鍦?)
                if not os.path.exists(self.watermark_path.get()):
                    raise FileNotFoundError("姘村嵃鍥剧墖涓嶅瓨鍦?)
                
                # 淇敼涓存椂鏂囦欢璺緞锛屼娇鐢ㄨ棰戠储寮曢伩鍏嶅啿绐?                temp_video_path = os.path.join(
                    self.output_path.get(), 
                    f"temp_{video_index}_{int(time.time())}.mp4"
                )
                temp_audio_path = os.path.join(
                    self.output_path.get(), 
                    f"temp_audio_{video_index}_{int(time.time())}.m4a"
                )
                
                video = cv2.VideoCapture(video_path)
                if not video.isOpened():
                    raise Exception("鏃犳硶鎵撳紑瑙嗛鏂囦欢")
                
                # 鑾峰彇瑙嗛淇℃伅
                frame_width = int(video.get(cv2.CAP_PROP_FRAME_WIDTH))
                frame_height = int(video.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = video.get(cv2.CAP_PROP_FPS)
                
                # 鍒涘缓杈撳嚭瑙嗛
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                output_video = cv2.VideoWriter(
                    temp_video_path, 
                    fourcc, 
                    fps, 
                    (frame_width, frame_height), 
                    isColor=True
                )
                
                # 璇诲彇鍥剧墖姘村嵃
                watermark = None
                if self.watermark_path.get():
                    watermark = cv2.imread(self.watermark_path.get(), cv2.IMREAD_UNCHANGED)
                    if watermark is None:
                        raise Exception("鏃犳硶璇诲彇姘村嵃鍥剧墖")
                    # 璋冩暣姘村嵃澶у皬
                    watermark = cv2.resize(watermark, (self.watermark_width.get(), self.watermark_height.get()))
                
                # 鍑嗗鏂囨湰姘村嵃
                text = self.text_input.get("1.0", tk.END).strip()
                font = None
                text_width = text_height = 0
                
                if text:
                    try:
                        font = ImageFont.truetype("msyh.ttc", self.font_size.get())
                    except:
                        try:
                            font = ImageFont.truetype("simhei.ttf", self.font_size.get())
                        except:
                            font = ImageFont.load_default()
                    
                    # 鑾峰彇鏂囨湰澶у皬
                    text_bbox = font.getbbox(text)
                    text_width = text_bbox[2] - text_bbox[0]
                    text_height = text_bbox[3] - text_bbox[1]
                
                # 鍥剧墖姘村嵃鐨勫垵濮嬩綅缃拰閫熷害
                x_offset, y_offset = 20, 20
                x_speed = y_speed = self.speed.get()
                
                # 澶勭悊姣忎竴甯?                while True:
                    ret, frame = video.read()
                    if not ret:
                        break
                    
                    # 娣诲姞鍥剧墖姘村嵃
                    if watermark is not None:
                        # 鏇存柊姘村嵃浣嶇疆
                        y_offset += y_speed
                        x_offset += x_speed
                        
                        # 杈圭晫妫€鏌?                        if y_offset + watermark.shape[0] > frame_height or y_offset < 0:
                            y_speed *= -1
                            y_offset = max(0, min(y_offset, frame_height - watermark.shape[0]))
                        if x_offset + watermark.shape[1] > frame_width or x_offset < 0:
                            x_speed *= -1
                            x_offset = max(0, min(x_offset, frame_width - watermark.shape[1]))
                        
                        # 娣诲姞姘村嵃
                        y1, y2 = int(y_offset), int(y_offset + watermark.shape[0])
                        x1, x2 = int(x_offset), int(x_offset + watermark.shape[1])
                        
                        if watermark.shape[2] == 4:  # 甯﹂€忔槑閫氶亾
                            alpha_s = watermark[:, :, 3] / 255.0 * self.transparent.get()
                            alpha_l = 1.0 - alpha_s
                            
                            for c in range(0, 3):
                                frame[y1:y2, x1:x2, c] = (alpha_s * watermark[:, :, c] +
                                                         alpha_l * frame[y1:y2, x1:x2, c])
                    
                    # 娣诲姞鏂囨湰姘村嵃
                    if text:
                        # 鍒嗗壊澶氳鏂囨湰
                        lines = text.split('\n')
                        
                        # 鍒涘缓 PIL Image 瀵硅薄
                        pil_image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                        draw = ImageDraw.Draw(pil_image)
                        
                        # 璁＄畻鍩哄噯y鍧愭爣
                        ratio = self.height_ratio.get()
                        base_y = frame_height - frame_height // ratio
                        
                        # 璁＄畻鎵€鏈夎鐨勬€婚珮搴?                        line_spacing = 10  # 琛岄棿璺?                        total_height = 0
                        for line in lines:
                            bbox = font.getbbox(line)
                            total_height += (bbox[3] - bbox[1]) + line_spacing
                        
                        # 璋冩暣璧峰y鍧愭爣锛岃€冭檻鎬婚珮搴?                        current_y = base_y - total_height
                        
                        # 閫愯缁樺埗鏂囨湰锛屾瘡琛屽眳涓?                        for line in lines:
                            # 鑾峰彇褰撳墠琛岀殑瀹藉害
                            bbox = font.getbbox(line)
                            line_width = bbox[2] - bbox[0]
                            
                            # 璁＄畻褰撳墠琛岀殑x鍧愭爣锛堝眳涓級
                            x = (frame_width - line_width) // 2
                            
                            # 缁樺埗褰撳墠琛?                            draw.text((x, current_y), line, font=font, fill=self.text_color.get())
                            
                            # 鏇存柊y鍧愭爣鍒颁笅涓€琛?                            current_y += (bbox[3] - bbox[1]) + line_spacing
                        
                        # 杞洖 OpenCV 鏍煎紡
                        frame = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
                    
                    # 鍐欏叆杈撳嚭瑙嗛
                    output_video.write(frame)
                
                # 閲婃斁OpenCV璧勬簮
                if video is not None:
                    video.release()
                if output_video is not None:
                    output_video.release()
                cv2.destroyAllWindows()
                
                # 浣跨敤moviepy鍚堝苟瑙嗛鍜岄煶棰?                self.status_var.set("姝ｅ湪鍚堝苟闊抽...")
                self.root.update()
                
                try:
                    # 鑾峰彇绋嬪簭杩愯鐩綍
                    if getattr(sys, 'frozen', False):
                        # 濡傛灉鏄墦鍖呭悗鐨勭▼搴?                        application_path = os.path.dirname(sys.executable)
                    else:
                        # 濡傛灉鏄紑鍙戠幆澧?                        application_path = os.path.dirname(os.path.abspath(__file__))
                        
                    # 璁剧疆 ImageMagick 璺緞
                    imagemagick_path = os.path.join(application_path, 'ImageMagick-7.0.10-Q16', 'magick.exe')
                    if os.path.exists(imagemagick_path):
                        # 璁剧疆 ImageMagick 鐨勫彲鎵ц鏂囦欢璺緞,鐢ㄤ簬 moviepy 澶勭悊瑙嗛鏃惰皟鐢?ImageMagick 杩涜鍥惧儚澶勭悊
                        change_settings({"IMAGEMAGICK_BINARY": imagemagick_path})
                    
                    # 璇诲彇鍘熷瑙嗛鐨勯煶棰?                    original_video = VideoFileClip(video_path)
                    if original_video.audio is None:
                        # 濡傛灉鍘熻棰戞病鏈夐煶棰戯紝鐩存帴閲嶅懡鍚嶄复鏃舵枃浠朵负鏈€缁堟枃浠?                        if os.path.exists(temp_video_path):
                            final_output_path = os.path.join(self.output_path.get(), "watermarked_" + os.path.basename(video_path))
                            if os.path.exists(final_output_path):
                                os.remove(final_output_path)
                            os.rename(temp_video_path, final_output_path)
                            return True
                    
                    # 璇诲彇娣诲姞浜嗘按鍗扮殑瑙嗛
                    watermarked_video = VideoFileClip(temp_video_path)
                    if watermarked_video is None:
                        raise Exception("鏃犳硶璇诲彇澶勭悊鍚庣殑瑙嗛鏂囦欢")
                        
                    # 璁剧疆闊抽
                    final_video = watermarked_video.set_audio(original_video.audio)
                    if final_video is None:
                        raise Exception("鍚堝苟闊抽澶辫触")
                        
                    # 淇濆瓨鏈€缁堣棰?                    final_output_path = os.path.join(self.output_path.get(), "watermarked_" + os.path.basename(video_path))
                    if os.path.exists(final_output_path):
                        os.remove(final_output_path)
                        
                    final_video.write_videofile(
                        final_output_path,
                        codec='libx264',
                        audio_codec='aac',
                        temp_audiofile=temp_audio_path,  # 浣跨敤鍞竴鐨勪复鏃堕煶棰戞枃浠惰矾寰?                        remove_temp=True,
                        verbose=False,
                        logger=None
                    )
                    
                except Exception as e:
                    raise Exception(f"闊抽澶勭悊澶辫触: {str(e)}")
                
                finally:
                    # 纭繚鍏抽棴鎵€鏈塵oviepy鐨勮棰戝璞?                    try:
                        if original_video is not None:
                            original_video.close()
                        if watermarked_video is not None:
                            watermarked_video.close()
                        if final_video is not None:
                            final_video.close()
                    except Exception as e:
                        print(f"鍏抽棴瑙嗛瀵硅薄鏃跺嚭閿? {e}")
                    
                    # 绛夊緟涓€娈垫椂闂寸‘淇濇枃浠跺彞鏌勮閲婃斁
                    time.sleep(1)
                    
                    # 鍒犻櫎涓存椂鏂囦欢
                    for temp_file in [temp_video_path, temp_audio_path]:
                        if temp_file and os.path.exists(temp_file):
                            try:
                                os.remove(temp_file)
                            except Exception as e:
                                print(f"鍒犻櫎涓存椂鏂囦欢澶辫触: {e}")

                # 鍦ㄦ垚鍔熷畬鎴愭墍鏈夊鐞嗗悗璁剧疆鎴愬姛鏍囧織
                success = True
                
            except Exception as e:
                # 璁板綍閿欒浣嗕笉鏄剧ず娑堟伅妗嗭紙閬垮厤澶氭寮圭獥锛?                print(f"澶勭悊瑙嗛 {os.path.basename(video_path)} 鏃跺嚭閿欙細{str(e)}")
                self.status_var.set(f"澶勭悊澶辫触: {os.path.basename(video_path)}")
            finally:
                # 娓呯悊璧勬簮
                if video is not None:
                    video.release()
                if output_video is not None:
                    output_video.release()
                cv2.destroyAllWindows()
                
                # 鍏抽棴 moviepy 瀵硅薄
                for clip in [original_video, watermarked_video, final_video]:
                    if clip is not None:
                        try:
                            clip.close()
                        except:
                            pass
                
                # 鍒犻櫎涓存椂鏂囦欢
                for temp_file in [temp_video_path, temp_audio_path]:
                    if temp_file and os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except:
                            pass
                
                # 鏇存柊杩涘害
                progress_var.set(video_index + 1)
                self.root.update()
            return success  # 杩斿洖澶勭悊缁撴灉

        try:
            # 鍒涘缓鎵€鏈変换鍔?            tasks = [process_video(path, idx) for idx, path in enumerate(video_paths)]
            
            # 骞跺彂鎵ц鎵€鏈変换鍔?            results = await asyncio.gather(*tasks)
            
            # 缁熻缁撴灉
            success_count = sum(1 for r in results if r)
            failed_count = len(results) - success_count
            
            # 鏄剧ず缁撴灉
            if failed_count == 0:
                self.status_var.set("鎵€鏈夎棰戝鐞嗗畬鎴愶紒")
                messagebox.showinfo("瀹屾垚", f"宸叉垚鍔熷鐞?{success_count} 涓棰戯紒")
            else:
                self.status_var.set(f"澶勭悊瀹屾垚 (鎴愬姛: {success_count}, 澶辫触: {failed_count})")
                messagebox.showwarning("瀹屾垚", 
                    f"澶勭悊瀹屾垚\n鎴愬姛: {success_count} 涓猏n澶辫触: {failed_count} 涓?)
        
        except Exception as e:
            messagebox.showerror("閿欒", f"鎵归噺澶勭悊杩囩▼涓嚭鐜伴敊璇細{str(e)}")
            self.status_var.set("鎵归噺澶勭悊澶辫触")
        finally:
            progress_frame.destroy()

    def start_process(self):
        if not self.video_path.get() or not self.output_path.get():
            messagebox.showwarning("璀﹀憡", "璇烽€夋嫨瑙嗛鏂囦欢鍜岃緭鍑鸿矾寰?)
            return
        
        # 鍚姩寮傛澶勭悊
        async def run_async():
            await self.add_watermark()
        
        def start_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(run_async())
            finally:
                loop.close()
        
        Thread(target=start_async, daemon=True).start()

    def run(self):
        self.root.mainloop()

    def choose_color(self):
        from tkinter import colorchooser
        color = colorchooser.askcolor(title="閫夋嫨鏂囧瓧棰滆壊", color=self.text_color.get())
        if color[1]:
            self.text_color.set(color[1])

    def update_position_controls(self):
        """鏍规嵁閫夋嫨鐨勪綅缃被鍨嬫樉绀哄搴旂殑鎺т欢"""
        # 鍏堥殣钘忔墍鏈夋帶浠?        self.height_ratio_frame.pack_forget()
        self.width_ratio_frame.pack_forget()
        
        # 鏍规嵁閫夋嫨鏄剧ず瀵瑰簲鎺т欢
        if self.text_position.get() == "bottom-center":
            self.height_ratio_frame.pack(side="left", padx=5)
        else:  # bottom-left 鎴?bottom-right
            self.width_ratio_frame.pack(side="left", padx=5)

    def get_text_position(self, frame_width, frame_height, text_size):
        """璁＄畻鏂囨湰浣嶇疆
        Args:
            frame_width: 瑙嗛甯у搴?            frame_height: 瑙嗛甯ч珮搴?            text_size: (鏂囨湰瀹藉害, 鏂囨湰楂樺害)
        Returns:
            (x, y): 鏂囨湰浣嶇疆鍧愭爣
        """
        text_width, text_height = text_size
        position = self.text_position.get()
        
        if position == "bottom-center":
            # 搴曢儴灞呬腑锛屼娇鐢ㄩ珮搴︽瘮渚?            ratio = self.height_ratio.get()
            y = frame_height - frame_height // ratio - text_height - 20  # 搴曢儴鐣欏嚭20鍍忕礌杈硅窛
            x = (frame_width - text_width) // 2  # 姘村钩灞呬腑
        elif position == "left-center":
            # 搴曢儴宸︿晶锛屼娇鐢ㄥ搴︽瘮渚?            ratio = self.width_ratio.get()
            x = frame_width // ratio
            y = frame_height - text_height - 20  # 搴曢儴鐣欏嚭20鍍忕礌杈硅窛
        else:  # bottom-right
            # 搴曢儴鍙充晶锛屼娇鐢ㄥ搴︽瘮渚?            ratio = self.width_ratio.get()
            x = frame_width - frame_width // ratio - text_width
            y = frame_height - text_height - 20  # 搴曢儴鐣欏嚭20鍍忕礌杈硅窛
        
        return int(x), int(y)

    def select_remove_video(self):
        filenames = filedialog.askopenfilenames(filetypes=[("瑙嗛鏂囦欢", "*.mp4")])
        if filenames:
            self.remove_video_path.set(";".join(filenames))
            self.remove_file_count_label.config(text=f"宸查€夋嫨 {len(filenames)} 涓枃浠?)

    def select_remove_output(self):
        folder = filedialog.askdirectory()
        if folder:
            self.remove_output_path.set(folder)

    async def remove_watermark(self):
        try:
            from delete_watermark import WatermarkRemover
            
            # 鑾峰彇鎵€鏈夎棰戞枃浠惰矾寰?            video_paths = self.remove_video_path.get().split(";")
            total_videos = len(video_paths)
            
            for idx, video_path in enumerate(video_paths):
                try:
                    # 鏇存柊鐘舵€?                    self.status_var.set(f"姝ｅ湪澶勭悊: {os.path.basename(video_path)} ({idx + 1}/{total_videos})")
                    self.root.update()
                    
                    # 鍒涘缓杈撳嚭鏂囦欢璺緞
                    output_path = os.path.join(
                        self.remove_output_path.get(),
                        f"removed_watermark_{os.path.basename(video_path)}"
                    )
                    
                    # 鍒涘缓鍘绘按鍗板鐞嗗櫒瀹炰緥
                    remover = WatermarkRemover()
                    
                    # 澶勭悊瑙嗛
                    remover.process_video(video_path, output_path)
                    
                    # 鏇存柊杩涘害
                    self.remove_progress_var.set((idx + 1) / total_videos * 100)
                    self.root.update()
                    
                except Exception as e:
                    print(f"澶勭悊瑙嗛 {os.path.basename(video_path)} 鏃跺嚭閿欙細{str(e)}")
                    self.status_var.set(f"澶勭悊澶辫触: {os.path.basename(video_path)}")
            
            messagebox.showinfo("瀹屾垚", "姘村嵃鍘婚櫎瀹屾垚锛?)
            self.status_var.set("姘村嵃鍘婚櫎瀹屾垚")
            
        except Exception as e:
            messagebox.showerror("閿欒", f"鍘婚櫎姘村嵃杩囩▼涓嚭鐜伴敊璇細{str(e)}")
            self.status_var.set("鍘婚櫎姘村嵃澶辫触")

    def start_remove_watermark(self):
        if not self.remove_video_path.get() or not self.remove_output_path.get():
            messagebox.showwarning("璀﹀憡", "璇烽€夋嫨瑙嗛鏂囦欢鍜岃緭鍑鸿矾寰?)
            return
        
        # 鍚姩寮傛澶勭悊
        async def run_async():
            await self.remove_watermark()
        
        def start_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(run_async())
            finally:
                loop.close()
        
        Thread(target=start_async, daemon=True).start()

    def update_progress(self, progress):
        """鏇存柊杩涘害鏉＄殑鏂规硶"""
        self.remove_progress_var.set(progress)
        self.root.update()

    async def remove_text_watermark(self):
        try:
            from delete_watermark import WatermarkRemover
            from PIL import ImageFont
            
            # 鑾峰彇鎵€鏈夎棰戞枃浠惰矾寰?            video_paths = self.remove_video_path.get().split(";")
            total_videos = len(video_paths)
            
            for idx, video_path in enumerate(video_paths):
                try:
                    # 鏇存柊鐘舵€?                    self.status_var.set(f"璇峰湪瑙嗛绐楀彛涓閫夎鍘婚櫎鐨勬按鍗板尯鍩燂紝鎸塃nter閿‘璁?)
                    self.root.update()
                    
                    # 鍒涘缓杈撳嚭鏂囦欢璺緞
                    output_path = os.path.join(
                        self.remove_output_path.get(),
                        f"removed_text_{os.path.basename(video_path)}"
                    )
                    
                    # 鍒涘缓鍘绘按鍗板鐞嗗櫒瀹炰緥
                    remover = WatermarkRemover()
                    
                    # 鏄剧ず瑙嗛绗竴甯х敤浜庨€夋嫨鍖哄煙
                    remover.select_roi(video_path)
                    
                    # 鏇存柊鐘舵€?                    self.status_var.set(f"姝ｅ湪澶勭悊: {os.path.basename(video_path)} ({idx + 1}/{total_videos})")
                    self.root.update()
                    
                    # 閲嶇疆杩涘害鏉?                    self.remove_progress_var.set(0)
                    
                    def progress_callback(frame_idx, total_frames):
                        # 浣跨敤 after 鏂规硶纭繚鍦ㄤ富绾跨▼涓洿鏂?UI
                        progress = (frame_idx / total_frames) * 100
                        self.root.after(0, lambda: self.update_progress(progress))
                    
                    # 澶勭悊瑙嗛锛屼紶鍏ヨ繘搴﹀洖璋冨嚱鏁?                    remover.process_video(video_path, output_path, progress_callback)
                    
                    # 鏇存柊鎬讳綋杩涘害
                    total_progress = ((idx + 1) / total_videos) * 100
                    self.remove_progress_var.set(total_progress)
                    self.root.update()
                    
                except Exception as e:
                    print(f"澶勭悊瑙嗛 {os.path.basename(video_path)} 鏃跺嚭閿欙細{str(e)}")
                    self.status_var.set(f"鍘婚櫎鏂囨湰姘村嵃澶辫触: {os.path.basename(video_path)}")
            
            messagebox.showinfo("瀹屾垚", "鏂囨湰姘村嵃鍘婚櫎瀹屾垚锛?)
            self.status_var.set("鏂囨湰姘村嵃鍘婚櫎瀹屾垚")
            
        except Exception as e:
            messagebox.showerror("閿欒", f"鍘婚櫎鏂囨湰姘村嵃杩囩▼涓嚭鐜伴敊璇細{str(e)}")
            self.status_var.set("鍘婚櫎鏂囨湰姘村嵃澶辫触")

    def start_remove_text_watermark(self):
        if not self.remove_video_path.get() or not self.remove_output_path.get():
            messagebox.showwarning("璀﹀憡", "璇烽€夋嫨瑙嗛鏂囦欢鍜岃緭鍑鸿矾寰?)
            return
        
        # 鍚姩寮傛澶勭悊
        async def run_async():
            await self.remove_text_watermark()
        
        def start_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(run_async())
            finally:
                loop.close()
        
        Thread(target=start_async, daemon=True).start()

if __name__ == "__main__":
    app = VideoWatermark()
    app.run() 
