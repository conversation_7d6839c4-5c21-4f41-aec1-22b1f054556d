@echo off
chcp 65001 >nul
title 视频水印工具 - 快速打包

echo ========================================
echo 视频水印工具 PyInstaller 快速打包
echo ========================================
echo.

REM 检查 Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到 Python，请先安装 Python
    pause
    exit /b 1
)
echo [✓] Python 已安装

REM 安装 PyInstaller
echo [信息] 检查 PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if %errorlevel% neq 0 (
    echo [信息] 安装 PyInstaller...
    pip install pyinstaller
    if %errorlevel% neq 0 (
        echo [错误] PyInstaller 安装失败
        pause
        exit /b 1
    )
)
echo [✓] PyInstaller 已准备

REM 清理旧文件
echo [信息] 清理旧文件...
if exist dist rmdir /s /q dist >nul 2>&1
if exist build rmdir /s /q build >nul 2>&1
echo [✓] 清理完成

REM 开始打包
echo.
echo [信息] 开始打包...
echo [命令] pyinstaller --onefile --windowed --name="视频水印工具" --add-data="config.ini;." main.py
echo.

pyinstaller --onefile --windowed --name="视频水印工具" --add-data="config.ini;." main.py

REM 检查结果
if exist "dist\视频水印工具.exe" (
    echo.
    echo ========================================
    echo [成功] 打包完成！
    echo ========================================
    echo 可执行文件: dist\视频水印工具.exe
    
    REM 显示文件大小
    for %%I in ("dist\视频水印工具.exe") do (
        set /a size=%%~zI/1024/1024
        echo 文件大小: !size! MB
    )
    
    echo.
    set /p test="是否测试运行？(y/n): "
    if /i "!test!"=="y" (
        echo [信息] 启动程序...
        start "" "dist\视频水印工具.exe"
    )
    
) else (
    echo.
    echo ========================================
    echo [失败] 打包失败！
    echo ========================================
    echo 请检查错误信息，可能需要：
    echo 1. 安装缺失的依赖包
    echo 2. 添加隐藏导入模块
    echo 3. 检查文件路径
    echo.
    echo 尝试手动安装依赖：
    echo pip install moviepy opencv-python ttkbootstrap pillow numpy tqdm wmi
)

echo.
pause
