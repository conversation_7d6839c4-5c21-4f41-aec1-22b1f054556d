('F:\\soft\\python\\workdata\\vidieo_mark\\build\\main\\PYZ-00.pyz',
 [('PIL',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_compression.py',
   'PYMODULE'),
  ('_distutils_hack',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_osx_support',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('backports',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('base64',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cffi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.api',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\cgi.py',
   'PYMODULE'),
  ('charset_normalizer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\codeop.py',
   'PYMODULE'),
  ('colorama',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\colorsys.py',
   'PYMODULE'),
  ('commctrl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\decimal.py',
   'PYMODULE'),
  ('decorator',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\decorator.py',
   'PYMODULE'),
  ('difflib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\dis.py',
   'PYMODULE'),
  ('distutils',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\doctest.py',
   'PYMODULE'),
  ('email',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fileinput',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('imageio',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\__init__.py',
   'PYMODULE'),
  ('imageio.config',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\config\\__init__.py',
   'PYMODULE'),
  ('imageio.config.extensions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\config\\extensions.py',
   'PYMODULE'),
  ('imageio.config.plugins',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\config\\plugins.py',
   'PYMODULE'),
  ('imageio.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\__init__.py',
   'PYMODULE'),
  ('imageio.core.fetching',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\fetching.py',
   'PYMODULE'),
  ('imageio.core.findlib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\findlib.py',
   'PYMODULE'),
  ('imageio.core.format',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\format.py',
   'PYMODULE'),
  ('imageio.core.imopen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\imopen.py',
   'PYMODULE'),
  ('imageio.core.legacy_plugin_wrapper',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\legacy_plugin_wrapper.py',
   'PYMODULE'),
  ('imageio.core.request',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\request.py',
   'PYMODULE'),
  ('imageio.core.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\util.py',
   'PYMODULE'),
  ('imageio.core.v3_plugin_api',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\v3_plugin_api.py',
   'PYMODULE'),
  ('imageio.plugins',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\__init__.py',
   'PYMODULE'),
  ('imageio.plugins._bsdf',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\_bsdf.py',
   'PYMODULE'),
  ('imageio.plugins._dicom',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\_dicom.py',
   'PYMODULE'),
  ('imageio.plugins._freeimage',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\_freeimage.py',
   'PYMODULE'),
  ('imageio.plugins._swf',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\_swf.py',
   'PYMODULE'),
  ('imageio.plugins._tifffile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\_tifffile.py',
   'PYMODULE'),
  ('imageio.plugins.bsdf',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\bsdf.py',
   'PYMODULE'),
  ('imageio.plugins.dicom',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\dicom.py',
   'PYMODULE'),
  ('imageio.plugins.example',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\example.py',
   'PYMODULE'),
  ('imageio.plugins.feisem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\feisem.py',
   'PYMODULE'),
  ('imageio.plugins.ffmpeg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\ffmpeg.py',
   'PYMODULE'),
  ('imageio.plugins.fits',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\fits.py',
   'PYMODULE'),
  ('imageio.plugins.freeimage',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\freeimage.py',
   'PYMODULE'),
  ('imageio.plugins.freeimagemulti',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\freeimagemulti.py',
   'PYMODULE'),
  ('imageio.plugins.gdal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\gdal.py',
   'PYMODULE'),
  ('imageio.plugins.grab',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\grab.py',
   'PYMODULE'),
  ('imageio.plugins.lytro',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\lytro.py',
   'PYMODULE'),
  ('imageio.plugins.npz',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\npz.py',
   'PYMODULE'),
  ('imageio.plugins.opencv',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\opencv.py',
   'PYMODULE'),
  ('imageio.plugins.pillow',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\pillow.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_info',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\pillow_info.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_legacy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\pillow_legacy.py',
   'PYMODULE'),
  ('imageio.plugins.pillowmulti',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\pillowmulti.py',
   'PYMODULE'),
  ('imageio.plugins.pyav',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\pyav.py',
   'PYMODULE'),
  ('imageio.plugins.rawpy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\rawpy.py',
   'PYMODULE'),
  ('imageio.plugins.simpleitk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\simpleitk.py',
   'PYMODULE'),
  ('imageio.plugins.spe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\spe.py',
   'PYMODULE'),
  ('imageio.plugins.swf',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\swf.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\tifffile.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile_v3',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\tifffile_v3.py',
   'PYMODULE'),
  ('imageio.typing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\typing.py',
   'PYMODULE'),
  ('imageio.v2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\v2.py',
   'PYMODULE'),
  ('imageio.v3',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\v3.py',
   'PYMODULE'),
  ('imageio_ffmpeg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\__init__.py',
   'PYMODULE'),
  ('imageio_ffmpeg._definitions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\_definitions.py',
   'PYMODULE'),
  ('imageio_ffmpeg._io',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\_io.py',
   'PYMODULE'),
  ('imageio_ffmpeg._parsing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\_parsing.py',
   'PYMODULE'),
  ('imageio_ffmpeg._utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\_utils.py',
   'PYMODULE'),
  ('imageio_ffmpeg.binaries',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\binaries\\__init__.py',
   'PYMODULE'),
  ('imp',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\imp.py',
   'PYMODULE'),
  ('importlib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib_metadata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('inspect',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ipaddress.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('jaraco.collections',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\collections\\__init__.py',
   'PYMODULE'),
  ('json',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\mimetypes.py',
   'PYMODULE'),
  ('moviepy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\__init__.py',
   'PYMODULE'),
  ('moviepy.Clip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\Clip.py',
   'PYMODULE'),
  ('moviepy.audio',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\__init__.py',
   'PYMODULE'),
  ('moviepy.audio.AudioClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\AudioClip.py',
   'PYMODULE'),
  ('moviepy.audio.fx',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\__init__.py',
   'PYMODULE'),
  ('moviepy.audio.fx.all',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\all\\__init__.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_fadein',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\audio_fadein.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_fadeout',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\audio_fadeout.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_left_right',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\audio_left_right.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_loop',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\audio_loop.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_normalize',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\audio_normalize.py',
   'PYMODULE'),
  ('moviepy.audio.fx.volumex',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\volumex.py',
   'PYMODULE'),
  ('moviepy.audio.io',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\io\\__init__.py',
   'PYMODULE'),
  ('moviepy.audio.io.AudioFileClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\io\\AudioFileClip.py',
   'PYMODULE'),
  ('moviepy.audio.io.ffmpeg_audiowriter',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\io\\ffmpeg_audiowriter.py',
   'PYMODULE'),
  ('moviepy.audio.io.preview',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\io\\preview.py',
   'PYMODULE'),
  ('moviepy.audio.io.readers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\io\\readers.py',
   'PYMODULE'),
  ('moviepy.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\compat.py',
   'PYMODULE'),
  ('moviepy.config',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\config.py',
   'PYMODULE'),
  ('moviepy.config_defaults',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\config_defaults.py',
   'PYMODULE'),
  ('moviepy.decorators',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\decorators.py',
   'PYMODULE'),
  ('moviepy.editor',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\editor.py',
   'PYMODULE'),
  ('moviepy.tools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\tools.py',
   'PYMODULE'),
  ('moviepy.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\version.py',
   'PYMODULE'),
  ('moviepy.video',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.VideoClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\VideoClip.py',
   'PYMODULE'),
  ('moviepy.video.compositing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\compositing\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.compositing.CompositeVideoClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\compositing\\CompositeVideoClip.py',
   'PYMODULE'),
  ('moviepy.video.compositing.concatenate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\compositing\\concatenate.py',
   'PYMODULE'),
  ('moviepy.video.compositing.on_color',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\compositing\\on_color.py',
   'PYMODULE'),
  ('moviepy.video.compositing.transitions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\compositing\\transitions.py',
   'PYMODULE'),
  ('moviepy.video.fx',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.fx.accel_decel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\accel_decel.py',
   'PYMODULE'),
  ('moviepy.video.fx.all',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\all\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.fx.blackwhite',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\blackwhite.py',
   'PYMODULE'),
  ('moviepy.video.fx.blink',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\blink.py',
   'PYMODULE'),
  ('moviepy.video.fx.colorx',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\colorx.py',
   'PYMODULE'),
  ('moviepy.video.fx.crop',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\crop.py',
   'PYMODULE'),
  ('moviepy.video.fx.even_size',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\even_size.py',
   'PYMODULE'),
  ('moviepy.video.fx.fadein',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\fadein.py',
   'PYMODULE'),
  ('moviepy.video.fx.fadeout',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\fadeout.py',
   'PYMODULE'),
  ('moviepy.video.fx.freeze',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\freeze.py',
   'PYMODULE'),
  ('moviepy.video.fx.freeze_region',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\freeze_region.py',
   'PYMODULE'),
  ('moviepy.video.fx.gamma_corr',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\gamma_corr.py',
   'PYMODULE'),
  ('moviepy.video.fx.headblur',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\headblur.py',
   'PYMODULE'),
  ('moviepy.video.fx.invert_colors',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\invert_colors.py',
   'PYMODULE'),
  ('moviepy.video.fx.loop',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\loop.py',
   'PYMODULE'),
  ('moviepy.video.fx.lum_contrast',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\lum_contrast.py',
   'PYMODULE'),
  ('moviepy.video.fx.make_loopable',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\make_loopable.py',
   'PYMODULE'),
  ('moviepy.video.fx.margin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\margin.py',
   'PYMODULE'),
  ('moviepy.video.fx.mask_and',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\mask_and.py',
   'PYMODULE'),
  ('moviepy.video.fx.mask_color',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\mask_color.py',
   'PYMODULE'),
  ('moviepy.video.fx.mask_or',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\mask_or.py',
   'PYMODULE'),
  ('moviepy.video.fx.mirror_x',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\mirror_x.py',
   'PYMODULE'),
  ('moviepy.video.fx.mirror_y',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\mirror_y.py',
   'PYMODULE'),
  ('moviepy.video.fx.painting',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\painting.py',
   'PYMODULE'),
  ('moviepy.video.fx.resize',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\resize.py',
   'PYMODULE'),
  ('moviepy.video.fx.rotate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\rotate.py',
   'PYMODULE'),
  ('moviepy.video.fx.scroll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\scroll.py',
   'PYMODULE'),
  ('moviepy.video.fx.speedx',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\speedx.py',
   'PYMODULE'),
  ('moviepy.video.fx.supersample',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\supersample.py',
   'PYMODULE'),
  ('moviepy.video.fx.time_mirror',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\time_mirror.py',
   'PYMODULE'),
  ('moviepy.video.fx.time_symmetrize',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\time_symmetrize.py',
   'PYMODULE'),
  ('moviepy.video.io',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.io.ImageSequenceClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\ImageSequenceClip.py',
   'PYMODULE'),
  ('moviepy.video.io.VideoFileClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\VideoFileClip.py',
   'PYMODULE'),
  ('moviepy.video.io.downloader',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\downloader.py',
   'PYMODULE'),
  ('moviepy.video.io.ffmpeg_reader',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\ffmpeg_reader.py',
   'PYMODULE'),
  ('moviepy.video.io.ffmpeg_tools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\ffmpeg_tools.py',
   'PYMODULE'),
  ('moviepy.video.io.ffmpeg_writer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\ffmpeg_writer.py',
   'PYMODULE'),
  ('moviepy.video.io.gif_writers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\gif_writers.py',
   'PYMODULE'),
  ('moviepy.video.io.html_tools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\html_tools.py',
   'PYMODULE'),
  ('moviepy.video.io.preview',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\preview.py',
   'PYMODULE'),
  ('moviepy.video.io.sliders',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\sliders.py',
   'PYMODULE'),
  ('moviepy.video.tools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\tools\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.tools.drawing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\tools\\drawing.py',
   'PYMODULE'),
  ('multiprocessing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.records',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.testing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pickle.py',
   'PYMODULE'),
  ('pkg_resources',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkgutil',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\platform.py',
   'PYMODULE'),
  ('plistlib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\plistlib.py',
   'PYMODULE'),
  ('pprint',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pprint.py',
   'PYMODULE'),
  ('proglog',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\proglog\\__init__.py',
   'PYMODULE'),
  ('proglog.proglog',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\proglog\\proglog.py',
   'PYMODULE'),
  ('proglog.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\proglog\\version.py',
   'PYMODULE'),
  ('py_compile',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\py_compile.py',
   'PYMODULE'),
  ('pycparser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pythoncom',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pywin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('pywintypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\random.py',
   'PYMODULE'),
  ('requests',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('rlcompleter',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('runpy',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\selectors.py',
   'PYMODULE'),
  ('setuptools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._imp',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('setuptools._path',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._vendor.backports',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py38',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py39',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.functional',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\functional.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future.adapters',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.config',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.depends',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.dist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.errors',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.extension',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.glob',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.logging',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\signal.py',
   'PYMODULE'),
  ('site',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\site.py',
   'PYMODULE'),
  ('socket',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\socketserver.py',
   'PYMODULE'),
  ('ssl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.colorchooser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\tkinter\\colorchooser.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.font',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\font.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tokenize.py',
   'PYMODULE'),
  ('tqdm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm.cli',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE'),
  ('tqdm.gui',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE'),
  ('tqdm.notebook',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.std',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\std.py',
   'PYMODULE'),
  ('tqdm.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('tqdm.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tracemalloc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('ttkbootstrap',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.colorutils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\colorutils.py',
   'PYMODULE'),
  ('ttkbootstrap.constants',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\constants.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\dialogs\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.colorchooser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\dialogs\\colorchooser.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.colordropper',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\dialogs\\colordropper.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.dialogs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\dialogs\\dialogs.py',
   'PYMODULE'),
  ('ttkbootstrap.icons',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\icons.py',
   'PYMODULE'),
  ('ttkbootstrap.localization',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\localization\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.localization.msgcat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\localization\\msgcat.py',
   'PYMODULE'),
  ('ttkbootstrap.localization.msgs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\localization\\msgs.py',
   'PYMODULE'),
  ('ttkbootstrap.publisher',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\publisher.py',
   'PYMODULE'),
  ('ttkbootstrap.style',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\style.py',
   'PYMODULE'),
  ('ttkbootstrap.themes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\themes\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.themes.standard',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\themes\\standard.py',
   'PYMODULE'),
  ('ttkbootstrap.themes.user',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\themes\\user.py',
   'PYMODULE'),
  ('ttkbootstrap.tooltip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\tooltip.py',
   'PYMODULE'),
  ('ttkbootstrap.utility',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\utility.py',
   'PYMODULE'),
  ('ttkbootstrap.validation',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\validation.py',
   'PYMODULE'),
  ('ttkbootstrap.widgets',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\widgets.py',
   'PYMODULE'),
  ('ttkbootstrap.window',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\window.py',
   'PYMODULE'),
  ('tty',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('unittest',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.mock',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest.result',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\uu.py',
   'PYMODULE'),
  ('video_watermark',
   'F:\\soft\\python\\workdata\\vidieo_mark\\video_watermark.py',
   'PYMODULE'),
  ('webbrowser',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\webbrowser.py',
   'PYMODULE'),
  ('wheel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel._bdist_wheel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\_bdist_wheel.py',
   'PYMODULE'),
  ('wheel._setuptools_logging',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('wheel.cli',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel.metadata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.vendored',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('win32com',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.universal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32traceutil',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('wmi',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wmi.py',
   'PYMODULE'),
  ('xml',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.etree',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipp',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.glob',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE')])
