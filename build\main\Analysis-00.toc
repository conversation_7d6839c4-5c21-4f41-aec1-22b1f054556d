(['F:\\soft\\python\\workdata\\vidieo_mark\\main.py'],
 ['F:\\soft\\python\\workdata\\vidieo_mark'],
 ['moviepy',
  'moviepy.editor',
  'moviepy.config',
  'imageio',
  'imageio_ffmpeg',
  'proglog',
  'numpy',
  'PIL'],
 [('F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('ffmpeg\\ffmpeg.exe', 'F:\\soft\\ffmpeg\\bin\\ffmpeg.exe', 'DATA')],
 '3.9.21 (main, Dec 11 2024, 16:35:24) [MSC v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'F:\\soft\\python\\workdata\\vidieo_mark\\main.py', 'PYSOURCE')],
 [('pkg_resources',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.tags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('subprocess',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\subprocess.py',
   'PYMODULE'),
  ('selectors',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\selectors.py',
   'PYMODULE'),
  ('contextlib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\contextlib.py',
   'PYMODULE'),
  ('signal',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\signal.py',
   'PYMODULE'),
  ('struct',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\struct.py',
   'PYMODULE'),
  ('logging',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pprint.py',
   'PYMODULE'),
  ('_compat_pickle',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('string',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\string.py',
   'PYMODULE'),
  ('packaging.metadata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('email.policy',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._encoded_words',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\getopt.py',
   'PYMODULE'),
  ('gettext',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\gettext.py',
   'PYMODULE'),
  ('copy',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\copy.py',
   'PYMODULE'),
  ('urllib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.charset',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\quopri.py',
   'PYMODULE'),
  ('email.quoprimime',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.errors',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.contentmanager',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.utils',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\calendar.py',
   'PYMODULE'),
  ('argparse',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\argparse.py',
   'PYMODULE'),
  ('shutil',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\gzip.py',
   'PYMODULE'),
  ('_compression',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_compression.py',
   'PYMODULE'),
  ('lzma',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\bz2.py',
   'PYMODULE'),
  ('fnmatch',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\fnmatch.py',
   'PYMODULE'),
  ('urllib.parse',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ipaddress.py',
   'PYMODULE'),
  ('datetime',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_strptime.py',
   'PYMODULE'),
  ('socket',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\socket.py',
   'PYMODULE'),
  ('random',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\numbers.py',
   'PYMODULE'),
  ('bisect',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\bisect.py',
   'PYMODULE'),
  ('email._policybase',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.message',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.iterators',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\generator.py',
   'PYMODULE'),
  ('uu',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\optparse.py',
   'PYMODULE'),
  ('email.header',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.feedparser',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.licenses',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('dataclasses',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\dataclasses.py',
   'PYMODULE'),
  ('packaging._structures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('ast',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ast.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('ctypes',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('packaging._elffile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('sysconfig',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('importlib.util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('configparser',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\configparser.py',
   'PYMODULE'),
  ('csv',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\csv.py',
   'PYMODULE'),
  ('tokenize',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\token.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('typing_extensions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('py_compile',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\py_compile.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('_osx_support',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.log',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('queue',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\queue.py',
   'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('distutils.util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('shlex',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\shlex.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.command',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.dist',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('glob',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\glob.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('jaraco.collections',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\collections\\__init__.py',
   'PYMODULE'),
  ('site',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\site.py',
   'PYMODULE'),
  ('rlcompleter',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pydoc.py',
   'PYMODULE'),
  ('webbrowser',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\http\\server.py',
   'PYMODULE'),
  ('http',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('socketserver',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\socketserver.py',
   'PYMODULE'),
  ('mimetypes',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\mimetypes.py',
   'PYMODULE'),
  ('http.client',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\http\\client.py',
   'PYMODULE'),
  ('ssl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ssl.py',
   'PYMODULE'),
  ('html',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tty.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('concurrent.futures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\runpy.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\secrets.py',
   'PYMODULE'),
  ('hmac',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\hmac.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\netrc.py',
   'PYMODULE'),
  ('http.cookiejar',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('urllib.response',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('multiprocessing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\difflib.py',
   'PYMODULE'),
  ('unittest.result',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('asyncio',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('_distutils_hack',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.extension',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.errors',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.core',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\cgi.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('importlib.resources',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib._common',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py38',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.functional',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\functional.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future.adapters',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.future',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\future\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.compat.py39',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\compat\\py39.py',
   'PYMODULE'),
  ('zipp',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp._functools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\_functools.py',
   'PYMODULE'),
  ('zipp.glob',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zipp.compat.py310',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\compat\\py310.py',
   'PYMODULE'),
  ('zipp.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('zipp.compat.overlay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\zipp\\compat\\overlay.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py311',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\compat\\py311.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools.extension',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.dist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE'),
  ('wheel.macosx_libfile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\macosx_libfile.py',
   'PYMODULE'),
  ('wheel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\__init__.py',
   'PYMODULE'),
  ('wheel._setuptools_logging',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\_setuptools_logging.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('setuptools.glob',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools._path',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('wheel.wheelfile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\wheelfile.py',
   'PYMODULE'),
  ('wheel.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\util.py',
   'PYMODULE'),
  ('wheel.cli',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\cli\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.tags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\cli\\tags.py',
   'PYMODULE'),
  ('wheel.cli.convert',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\cli\\convert.py',
   'PYMODULE'),
  ('wheel._bdist_wheel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\_bdist_wheel.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._structures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.tags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._musllinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._elffile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._manylinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('wheel.vendored.packaging',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE'),
  ('wheel.vendored',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\__init__.py',
   'PYMODULE'),
  ('wheel.cli.pack',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\cli\\pack.py',
   'PYMODULE'),
  ('wheel.cli.unpack',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\cli\\unpack.py',
   'PYMODULE'),
  ('wheel.metadata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\metadata.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.requirements',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.specifiers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging.markers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._tokenizer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('wheel.vendored.packaging._parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools.installer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.errors',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('setuptools.command',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('dis',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\opcode.py',
   'PYMODULE'),
  ('setuptools._imp',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE'),
  ('backports',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.markers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('typing',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\typing.py',
   'PYMODULE'),
  ('zipimport',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\zipimport.py',
   'PYMODULE'),
  ('zipfile',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\zipfile.py',
   'PYMODULE'),
  ('textwrap',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\textwrap.py',
   'PYMODULE'),
  ('tempfile',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tempfile.py',
   'PYMODULE'),
  ('plistlib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\plistlib.py',
   'PYMODULE'),
  ('platform',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\platform.py',
   'PYMODULE'),
  ('pkgutil',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pkgutil.py',
   'PYMODULE'),
  ('inspect',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\inspect.py',
   'PYMODULE'),
  ('importlib.machinery',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.abc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\email\\parser.py',
   'PYMODULE'),
  ('__future__',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\__future__.py',
   'PYMODULE'),
  ('pathlib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pathlib.py',
   'PYMODULE'),
  ('PIL',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.features',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageGrab.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL._typing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('numpy.typing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy._utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy._globals',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._core.records',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.linalg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.lib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('doctest',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\doctest.py',
   'PYMODULE'),
  ('pdb',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\pdb.py',
   'PYMODULE'),
  ('code',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\codeop.py',
   'PYMODULE'),
  ('bdb',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\bdb.py',
   'PYMODULE'),
  ('cmd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\cmd.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL._util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\colorsys.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('cffi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('ctypes.util',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('cffi.verifier',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('imp',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\imp.py',
   'PYMODULE'),
  ('cffi.lock',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.Image',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL._version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('numpy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('charset_normalizer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('fileinput',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\fileinput.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.__config__',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('proglog',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\proglog\\__init__.py',
   'PYMODULE'),
  ('proglog.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\proglog\\version.py',
   'PYMODULE'),
  ('proglog.proglog',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\proglog\\proglog.py',
   'PYMODULE'),
  ('tqdm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\__init__.py',
   'PYMODULE'),
  ('tqdm.notebook',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\notebook.py',
   'PYMODULE'),
  ('tqdm.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\version.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\_dist_ver.py',
   'PYMODULE'),
  ('tqdm.std',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\std.py',
   'PYMODULE'),
  ('tqdm.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\utils.py',
   'PYMODULE'),
  ('colorama',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('tqdm.gui',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\gui.py',
   'PYMODULE'),
  ('tqdm.cli',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\cli.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm._monitor',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\tqdm\\_monitor.py',
   'PYMODULE'),
  ('imageio_ffmpeg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\__init__.py',
   'PYMODULE'),
  ('imageio_ffmpeg.binaries',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\binaries\\__init__.py',
   'PYMODULE'),
  ('imageio_ffmpeg._utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\_utils.py',
   'PYMODULE'),
  ('imageio_ffmpeg._io',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\_io.py',
   'PYMODULE'),
  ('imageio_ffmpeg._parsing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\_parsing.py',
   'PYMODULE'),
  ('imageio_ffmpeg._definitions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\_definitions.py',
   'PYMODULE'),
  ('imageio',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\__init__.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile_v3',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\tifffile_v3.py',
   'PYMODULE'),
  ('imageio.typing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\typing.py',
   'PYMODULE'),
  ('imageio.core.v3_plugin_api',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\v3_plugin_api.py',
   'PYMODULE'),
  ('imageio.core.request',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\request.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\tifffile.py',
   'PYMODULE'),
  ('imageio.plugins.swf',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\swf.py',
   'PYMODULE'),
  ('imageio.plugins.spe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\spe.py',
   'PYMODULE'),
  ('imageio.plugins.simpleitk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\simpleitk.py',
   'PYMODULE'),
  ('imageio.plugins.rawpy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\rawpy.py',
   'PYMODULE'),
  ('imageio.plugins.pyav',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\pyav.py',
   'PYMODULE'),
  ('imageio.plugins.pillowmulti',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\pillowmulti.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_legacy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\pillow_legacy.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_info',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\pillow_info.py',
   'PYMODULE'),
  ('imageio.plugins.pillow',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\pillow.py',
   'PYMODULE'),
  ('imageio.plugins.opencv',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\opencv.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('imageio.plugins.npz',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\npz.py',
   'PYMODULE'),
  ('imageio.plugins.lytro',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\lytro.py',
   'PYMODULE'),
  ('imageio.plugins.grab',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\grab.py',
   'PYMODULE'),
  ('imageio.plugins.gdal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\gdal.py',
   'PYMODULE'),
  ('imageio.plugins.freeimagemulti',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\freeimagemulti.py',
   'PYMODULE'),
  ('imageio.plugins.freeimage',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\freeimage.py',
   'PYMODULE'),
  ('imageio.plugins.fits',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\fits.py',
   'PYMODULE'),
  ('imageio.plugins.ffmpeg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\ffmpeg.py',
   'PYMODULE'),
  ('imageio.plugins.feisem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\feisem.py',
   'PYMODULE'),
  ('imageio.plugins.example',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\example.py',
   'PYMODULE'),
  ('imageio.plugins.dicom',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\dicom.py',
   'PYMODULE'),
  ('imageio.plugins.bsdf',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\bsdf.py',
   'PYMODULE'),
  ('imageio.plugins._tifffile',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\_tifffile.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('imageio.plugins._swf',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\_swf.py',
   'PYMODULE'),
  ('imageio.plugins._freeimage',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\_freeimage.py',
   'PYMODULE'),
  ('imageio.plugins._dicom',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\_dicom.py',
   'PYMODULE'),
  ('imageio.plugins._bsdf',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\_bsdf.py',
   'PYMODULE'),
  ('imageio.config',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\config\\__init__.py',
   'PYMODULE'),
  ('imageio.config.plugins',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\config\\plugins.py',
   'PYMODULE'),
  ('imageio.core.legacy_plugin_wrapper',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\legacy_plugin_wrapper.py',
   'PYMODULE'),
  ('imageio.config.extensions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\config\\extensions.py',
   'PYMODULE'),
  ('imageio.plugins',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\plugins\\__init__.py',
   'PYMODULE'),
  ('imageio.v3',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\v3.py',
   'PYMODULE'),
  ('imageio.core.imopen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\imopen.py',
   'PYMODULE'),
  ('imageio.v2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\v2.py',
   'PYMODULE'),
  ('imageio.core.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\util.py',
   'PYMODULE'),
  ('imageio.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\__init__.py',
   'PYMODULE'),
  ('imageio.core.format',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\format.py',
   'PYMODULE'),
  ('imageio.core.fetching',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\fetching.py',
   'PYMODULE'),
  ('imageio.core.findlib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio\\core\\findlib.py',
   'PYMODULE'),
  ('moviepy.editor',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\editor.py',
   'PYMODULE'),
  ('moviepy.audio.io.preview',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\io\\preview.py',
   'PYMODULE'),
  ('moviepy.audio.io',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\io\\__init__.py',
   'PYMODULE'),
  ('moviepy.audio',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\__init__.py',
   'PYMODULE'),
  ('moviepy.decorators',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\decorators.py',
   'PYMODULE'),
  ('decorator',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\decorator.py',
   'PYMODULE'),
  ('moviepy.video.io.preview',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\preview.py',
   'PYMODULE'),
  ('moviepy.video.io',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\__init__.py',
   'PYMODULE'),
  ('moviepy.video',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.io.sliders',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\sliders.py',
   'PYMODULE'),
  ('moviepy.tools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\tools.py',
   'PYMODULE'),
  ('moviepy.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\compat.py',
   'PYMODULE'),
  ('moviepy.video.io.html_tools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\html_tools.py',
   'PYMODULE'),
  ('moviepy.video.io.ffmpeg_reader',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\ffmpeg_reader.py',
   'PYMODULE'),
  ('moviepy.video.io.ffmpeg_tools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\ffmpeg_tools.py',
   'PYMODULE'),
  ('moviepy.video.tools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\tools\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.compositing.transitions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\compositing\\transitions.py',
   'PYMODULE'),
  ('moviepy.video.compositing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\compositing\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.fx.fadeout',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\fadeout.py',
   'PYMODULE'),
  ('moviepy.video.fx',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.fx.fadein',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\fadein.py',
   'PYMODULE'),
  ('moviepy.audio.fx.all',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\all\\__init__.py',
   'PYMODULE'),
  ('moviepy.audio.fx.volumex',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\volumex.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_normalize',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\audio_normalize.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_loop',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\audio_loop.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_left_right',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\audio_left_right.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_fadeout',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\audio_fadeout.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_fadein',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\audio_fadein.py',
   'PYMODULE'),
  ('moviepy.audio.fx',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\fx\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.fx.all',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\all\\__init__.py',
   'PYMODULE'),
  ('moviepy.video.fx.time_symmetrize',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\time_symmetrize.py',
   'PYMODULE'),
  ('moviepy.video.fx.time_mirror',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\time_mirror.py',
   'PYMODULE'),
  ('moviepy.video.fx.supersample',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\supersample.py',
   'PYMODULE'),
  ('moviepy.video.fx.speedx',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\speedx.py',
   'PYMODULE'),
  ('moviepy.video.fx.scroll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\scroll.py',
   'PYMODULE'),
  ('moviepy.video.fx.rotate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\rotate.py',
   'PYMODULE'),
  ('moviepy.video.fx.resize',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\resize.py',
   'PYMODULE'),
  ('moviepy.video.fx.painting',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\painting.py',
   'PYMODULE'),
  ('moviepy.video.fx.mirror_y',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\mirror_y.py',
   'PYMODULE'),
  ('moviepy.video.fx.mirror_x',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\mirror_x.py',
   'PYMODULE'),
  ('moviepy.video.fx.mask_or',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\mask_or.py',
   'PYMODULE'),
  ('moviepy.video.fx.mask_color',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\mask_color.py',
   'PYMODULE'),
  ('moviepy.video.fx.mask_and',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\mask_and.py',
   'PYMODULE'),
  ('moviepy.video.fx.margin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\margin.py',
   'PYMODULE'),
  ('moviepy.video.fx.make_loopable',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\make_loopable.py',
   'PYMODULE'),
  ('moviepy.video.fx.lum_contrast',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\lum_contrast.py',
   'PYMODULE'),
  ('moviepy.video.fx.loop',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\loop.py',
   'PYMODULE'),
  ('moviepy.video.fx.invert_colors',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\invert_colors.py',
   'PYMODULE'),
  ('moviepy.video.fx.headblur',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\headblur.py',
   'PYMODULE'),
  ('moviepy.video.fx.gamma_corr',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\gamma_corr.py',
   'PYMODULE'),
  ('moviepy.video.fx.freeze_region',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\freeze_region.py',
   'PYMODULE'),
  ('moviepy.video.fx.freeze',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\freeze.py',
   'PYMODULE'),
  ('moviepy.video.fx.even_size',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\even_size.py',
   'PYMODULE'),
  ('moviepy.video.fx.crop',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\crop.py',
   'PYMODULE'),
  ('moviepy.video.fx.colorx',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\colorx.py',
   'PYMODULE'),
  ('moviepy.video.fx.blink',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\blink.py',
   'PYMODULE'),
  ('moviepy.video.fx.blackwhite',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\blackwhite.py',
   'PYMODULE'),
  ('moviepy.video.fx.accel_decel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\fx\\accel_decel.py',
   'PYMODULE'),
  ('moviepy.audio.io.AudioFileClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\io\\AudioFileClip.py',
   'PYMODULE'),
  ('moviepy.audio.io.readers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\io\\readers.py',
   'PYMODULE'),
  ('moviepy.audio.AudioClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\AudioClip.py',
   'PYMODULE'),
  ('moviepy.Clip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\Clip.py',
   'PYMODULE'),
  ('moviepy.audio.io.ffmpeg_audiowriter',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\audio\\io\\ffmpeg_audiowriter.py',
   'PYMODULE'),
  ('moviepy.video.compositing.concatenate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\compositing\\concatenate.py',
   'PYMODULE'),
  ('moviepy.video.compositing.on_color',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\compositing\\on_color.py',
   'PYMODULE'),
  ('moviepy.video.compositing.CompositeVideoClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\compositing\\CompositeVideoClip.py',
   'PYMODULE'),
  ('moviepy.video.VideoClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\VideoClip.py',
   'PYMODULE'),
  ('moviepy.video.tools.drawing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\tools\\drawing.py',
   'PYMODULE'),
  ('moviepy.video.io.gif_writers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\gif_writers.py',
   'PYMODULE'),
  ('moviepy.video.io.ffmpeg_writer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\ffmpeg_writer.py',
   'PYMODULE'),
  ('moviepy.video.io.downloader',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\downloader.py',
   'PYMODULE'),
  ('requests',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('requests.models',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('requests.exceptions',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('moviepy.video.io.ImageSequenceClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\ImageSequenceClip.py',
   'PYMODULE'),
  ('moviepy.video.io.VideoFileClip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\video\\io\\VideoFileClip.py',
   'PYMODULE'),
  ('moviepy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\__init__.py',
   'PYMODULE'),
  ('moviepy.version',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\version.py',
   'PYMODULE'),
  ('_py_abc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('moviepy.config',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\config.py',
   'PYMODULE'),
  ('moviepy.config_defaults',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\moviepy\\config_defaults.py',
   'PYMODULE'),
  ('json',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('wmi',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wmi.py',
   'PYMODULE'),
  ('pywintypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('pywin32_system32', '-', 'PYMODULE'),
  ('win32com.client',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('winerror',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywin.mfc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('win32con',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('commctrl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.client.build',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.server.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32traceutil',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('win32com.util',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.universal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('pythoncom',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('hashlib',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\hashlib.py',
   'PYMODULE'),
  ('threading',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\_threading_local.py',
   'PYMODULE'),
  ('ttkbootstrap',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\__init__.py',
   'PYMODULE'),
  ('tkinter.scrolledtext',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\tkinter\\scrolledtext.py',
   'PYMODULE'),
  ('tkinter.constants',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('ttkbootstrap.window',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\window.py',
   'PYMODULE'),
  ('ttkbootstrap.icons',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\icons.py',
   'PYMODULE'),
  ('ttkbootstrap.publisher',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\publisher.py',
   'PYMODULE'),
  ('ttkbootstrap.constants',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\constants.py',
   'PYMODULE'),
  ('ttkbootstrap.widgets',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\widgets.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\dialogs\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.dialogs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\dialogs\\dialogs.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.colorchooser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\dialogs\\colorchooser.py',
   'PYMODULE'),
  ('ttkbootstrap.tooltip',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\tooltip.py',
   'PYMODULE'),
  ('ttkbootstrap.dialogs.colordropper',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\dialogs\\colordropper.py',
   'PYMODULE'),
  ('ttkbootstrap.validation',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\validation.py',
   'PYMODULE'),
  ('tkinter.font',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\font.py',
   'PYMODULE'),
  ('ttkbootstrap.style',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\style.py',
   'PYMODULE'),
  ('ttkbootstrap.themes.user',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\themes\\user.py',
   'PYMODULE'),
  ('ttkbootstrap.themes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\themes\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.themes.standard',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\themes\\standard.py',
   'PYMODULE'),
  ('ttkbootstrap.localization',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\localization\\__init__.py',
   'PYMODULE'),
  ('ttkbootstrap.localization.msgcat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\localization\\msgcat.py',
   'PYMODULE'),
  ('ttkbootstrap.localization.msgs',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\localization\\msgs.py',
   'PYMODULE'),
  ('ttkbootstrap.colorutils',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\colorutils.py',
   'PYMODULE'),
  ('ttkbootstrap.utility',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\ttkbootstrap\\utility.py',
   'PYMODULE'),
  ('video_watermark',
   'F:\\soft\\python\\workdata\\vidieo_mark\\video_watermark.py',
   'PYMODULE'),
  ('tkinter.colorchooser',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\tkinter\\colorchooser.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tkinter',
   'F:\\Program Files\\anaconda\\envs\\watermark\\lib\\tkinter\\__init__.py',
   'PYMODULE')],
 [('ffmpeg\\ffmpeg.exe', 'F:\\soft\\ffmpeg\\bin\\ffmpeg.exe', 'BINARY'),
  ('imageio_ffmpeg\\binaries\\ffmpeg-win-x86_64-v7.1.exe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\binaries\\ffmpeg-win-x86_64-v7.1.exe',
   'BINARY'),
  ('python39.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\python39.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-caad452230ae4ddb57899b8b3a33c55c.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-caad452230ae4ddb57899b8b3a33c55c.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-23ebcc0b37c8e3d074511f362feac48b.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy.libs\\msvcp140-23ebcc0b37c8e3d074511f362feac48b.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes39.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pywin32_system32\\pywintypes39.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom39.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pywin32_system32\\pythoncom39.dll',
   'BINARY'),
  ('select.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_webp.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_imagingft.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_imagingtk.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_imagingcms.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_imagingmath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\_cffi_backend.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_imaging.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\md.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\mtrand.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_sfc64.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_philox.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_pcg64.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_mt19937.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\bit_generator.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_generator.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_common.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('python3.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\python3.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('tk86t.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'F:\\soft\\python\\workdata\\vidieo_mark\\build\\main\\base_library.zip',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\LICENSE',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\WHEEL',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\METADATA',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\RECORD',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('numpy.libs\\.load-order-numpy-2.0.2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy.libs\\.load-order-numpy-2.0.2',
   'DATA'),
  ('imageio_ffmpeg\\binaries\\README.md',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\binaries\\README.md',
   'DATA'),
  ('cv2\\config.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config-3.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('certifi\\cacert.pem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tk_data\\text.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tk_data\\button.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tk_data\\license.terms',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tk_data\\images\\README',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tk_data\\console.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tk_data\\tclIndex',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('wheel-0.44.0.dist-info\\WHEEL',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel-0.44.0.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.44.0.dist-info\\METADATA',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel-0.44.0.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.44.0.dist-info\\LICENSE.txt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel-0.44.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.44.0.dist-info\\RECORD',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel-0.44.0.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.44.0.dist-info\\entry_points.txt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel-0.44.0.dist-info\\entry_points.txt',
   'DATA'),
  ('cv2\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA')])
