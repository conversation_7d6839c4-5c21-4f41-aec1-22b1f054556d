# PyInstaller 打包指南

## 🚀 快速打包（推荐）

### 1. 安装 PyInstaller
```bash
pip install pyinstaller
```

### 2. 基础打包命令
```bash
# 最简单的打包（单文件，无控制台）
pyinstaller --onefile --windowed main.py

# 指定程序名称
pyinstaller --onefile --windowed --name="视频水印工具" main.py

# 添加配置文件
pyinstaller --onefile --windowed --name="视频水印工具" --add-data="config.ini;." main.py
```

### 3. 完整打包命令（推荐）
```bash
pyinstaller --onefile --windowed --name="视频水印工具" \
    --add-data="config.ini;." \
    --hidden-import=moviepy \
    --hidden-import=cv2 \
    --hidden-import=ttkbootstrap \
    --hidden-import=PIL \
    --hidden-import=numpy \
    --hidden-import=tqdm \
    --hidden-import=wmi \
    --hidden-import=configparser \
    main.py
```

## 📋 详细步骤

### 步骤1: 准备环境
```bash
# 1. 确保所有依赖已安装
pip install moviepy opencv-python ttkbootstrap pillow numpy tqdm wmi

# 2. 安装打包工具
pip install pyinstaller

# 3. 检查主程序能否正常运行
python main.py
```

### 步骤2: 选择打包方式

#### 方式A: 单文件打包（推荐新手）
```bash
pyinstaller --onefile --windowed --name="视频水印工具" main.py
```
**优点**: 只有一个exe文件，方便分发  
**缺点**: 启动稍慢，文件较大

#### 方式B: 目录打包（推荐高级用户）
```bash
pyinstaller --windowed --name="视频水印工具" main.py
```
**优点**: 启动快，可以单独更新文件  
**缺点**: 文件较多，需要整个目录

### 步骤3: 添加必要文件
```bash
# 添加配置文件
--add-data="config.ini;."

# 添加图标（如果有）
--icon="icon.ico"

# 添加 FFmpeg（如果需要）
--add-binary="ffmpeg.exe;."
```

### 步骤4: 处理导入问题
```bash
# 如果出现模块导入错误，添加隐藏导入
--hidden-import=模块名

# 常见的隐藏导入
--hidden-import=moviepy
--hidden-import=cv2
--hidden-import=ttkbootstrap
--hidden-import=PIL
--hidden-import=numpy
--hidden-import=tqdm
--hidden-import=wmi
```

## 🛠️ 常见问题解决

### 问题1: "No module named 'xxx'"
**解决方案**: 添加隐藏导入
```bash
--hidden-import=xxx
```

### 问题2: 配置文件找不到
**解决方案**: 添加数据文件
```bash
--add-data="config.ini;."
```

### 问题3: FFmpeg 找不到
**解决方案**: 
1. 下载 FFmpeg 到项目目录
2. 添加到打包命令
```bash
--add-binary="ffmpeg.exe;."
```

### 问题4: 文件太大
**解决方案**: 
1. 使用虚拟环境
2. 排除不需要的模块
```bash
--exclude-module=matplotlib
--exclude-module=scipy
```

### 问题5: 启动慢
**解决方案**: 使用目录打包而非单文件
```bash
pyinstaller --windowed main.py  # 去掉 --onefile
```

## 📁 完整示例

### 创建 build.bat 文件
```batch
@echo off
echo 开始打包视频水印工具...

REM 清理旧文件
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build

REM 打包命令
pyinstaller --onefile --windowed --name="视频水印工具" ^
    --add-data="config.ini;." ^
    --hidden-import=moviepy ^
    --hidden-import=cv2 ^
    --hidden-import=ttkbootstrap ^
    --hidden-import=PIL ^
    --hidden-import=numpy ^
    --hidden-import=tqdm ^
    --hidden-import=wmi ^
    --hidden-import=configparser ^
    main.py

echo 打包完成！
echo 可执行文件位置: dist\视频水印工具.exe
pause
```

### 使用方法
1. 将上述内容保存为 `build.bat`
2. 双击运行即可

## 🎯 最佳实践

### 1. 使用虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# 安装依赖
pip install -r requirements.txt

# 打包
pyinstaller main.py
```

### 2. 创建 spec 文件（高级）
```bash
# 生成 spec 文件
pyinstaller --onefile main.py

# 编辑 main.spec 文件
# 然后使用 spec 文件打包
pyinstaller main.spec
```

### 3. 测试打包结果
```bash
# 在不同的机器上测试
# 确保没有依赖问题
dist\视频水印工具.exe
```

## 📦 分发准备

### 1. 文件清单
- `视频水印工具.exe` - 主程序
- `config.ini` - 配置文件
- `README.txt` - 使用说明

### 2. 创建安装包（可选）
使用 NSIS 或 Inno Setup 创建专业的安装程序

### 3. 数字签名（可选）
为 exe 文件添加数字签名以提高信任度

## 🔧 故障排除

### 查看详细错误
```bash
# 使用控制台模式查看错误
pyinstaller --console main.py

# 或者在命令行运行 exe
cd dist
视频水印工具.exe
```

### 常用调试参数
```bash
--debug=all          # 显示所有调试信息
--log-level=DEBUG    # 详细日志
--clean              # 清理缓存
--noconfirm          # 不询问覆盖
```

## 总结

PyInstaller 打包的基本流程：
1. **安装依赖** → `pip install pyinstaller`
2. **基础打包** → `pyinstaller --onefile --windowed main.py`
3. **添加文件** → `--add-data="config.ini;."`
4. **解决导入** → `--hidden-import=模块名`
5. **测试运行** → 在干净环境测试

选择适合您需求的打包方式，遇到问题时参考故障排除部分。
