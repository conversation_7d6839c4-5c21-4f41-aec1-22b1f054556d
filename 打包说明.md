# 视频水印工具 PyInstaller 打包说明

## 快速开始

### 方法1: 使用批处理文件（推荐）
```bash
# 直接双击运行
build.bat
```

### 方法2: 使用 Python 脚本
```bash
python build_exe.py
```

### 方法3: 手动命令行
```bash
# 安装依赖
pip install -r requirements.txt

# 打包
pyinstaller video_watermark_tool.spec
```

## 详细步骤

### 1. 环境准备

#### 安装 Python 依赖
```bash
pip install -r requirements.txt
```

#### 必需的依赖包
- `pyinstaller` - 打包工具
- `moviepy` - 视频处理
- `opencv-python` - 图像处理
- `ttkbootstrap` - UI 框架
- `pillow` - 图像库
- `numpy` - 数值计算
- `tqdm` - 进度条
- `wmi` - 系统信息

### 2. FFmpeg 配置

PyInstaller 需要包含 FFmpeg 可执行文件：

#### 选项1: 自动检测
脚本会自动查找以下路径的 FFmpeg：
- `F:\soft\ffmpeg\bin\ffmpeg.exe`
- `C:\ffmpeg\bin\ffmpeg.exe`
- `ffmpeg.exe`（当前目录）

#### 选项2: 手动指定
修改 `video_watermark_tool.spec` 文件中的 FFmpeg 路径：
```python
datas = [
    ('你的FFmpeg路径/ffmpeg.exe', 'ffmpeg'),
    ('config.ini', '.'),
]
```

### 3. 打包选项

#### 单文件打包（推荐）
```bash
pyinstaller --onefile --windowed --name="视频水印工具" main.py
```

#### 目录打包（启动更快）
```bash
pyinstaller --windowed --name="视频水印工具" main.py
```

#### 使用 spec 文件（最灵活）
```bash
pyinstaller video_watermark_tool.spec
```

### 4. 常用 PyInstaller 参数

| 参数 | 说明 |
|------|------|
| `--onefile` | 打包成单个可执行文件 |
| `--windowed` | 不显示控制台窗口 |
| `--name` | 指定可执行文件名称 |
| `--icon` | 指定图标文件 |
| `--add-data` | 添加数据文件 |
| `--add-binary` | 添加二进制文件 |
| `--hidden-import` | 添加隐藏导入 |
| `--exclude-module` | 排除模块 |
| `--clean` | 清理缓存 |
| `--noconfirm` | 不询问覆盖 |

### 5. 常见问题解决

#### 问题1: 模块导入错误
```bash
# 添加隐藏导入
--hidden-import=模块名
```

#### 问题2: 文件缺失
```bash
# 添加数据文件
--add-data="源文件;目标目录"
```

#### 问题3: 可执行文件过大
```bash
# 排除不需要的模块
--exclude-module=matplotlib
--exclude-module=scipy
```

#### 问题4: FFmpeg 找不到
确保 FFmpeg 路径正确，或将 `ffmpeg.exe` 复制到项目目录

### 6. 优化建议

#### 减小文件大小
1. 使用虚拟环境，只安装必要的包
2. 排除不需要的模块
3. 使用 UPX 压缩（`--upx-dir`）

#### 提高启动速度
1. 使用目录打包而非单文件
2. 减少隐藏导入
3. 优化代码结构

#### 提高兼容性
1. 在目标系统类似的环境中打包
2. 包含所有必要的 DLL 文件
3. 测试不同 Windows 版本

### 7. 打包后的文件结构

```
dist/
├── 视频水印工具.exe    # 主程序
├── config.ini          # 配置文件
├── 优化总结.md         # 说明文档
└── install.bat         # 安装脚本（可选）
```

### 8. 分发准备

#### 创建安装包
1. 使用 NSIS 或 Inno Setup 创建安装程序
2. 包含必要的运行时库
3. 创建桌面快捷方式

#### 测试清单
- [ ] 在干净的 Windows 系统上测试
- [ ] 测试所有功能是否正常
- [ ] 检查文件路径是否正确
- [ ] 验证配置文件读取
- [ ] 测试视频处理功能

### 9. 故障排除

#### 查看详细错误信息
```bash
# 在命令行运行可执行文件
视频水印工具.exe

# 或者使用控制台模式打包
pyinstaller --console main.py
```

#### 常见错误及解决方案

1. **ImportError: No module named 'xxx'**
   - 添加 `--hidden-import=xxx`

2. **FileNotFoundError: ffmpeg not found**
   - 检查 FFmpeg 路径配置

3. **tkinter 相关错误**
   - 确保 Python 安装包含 tkinter

4. **DLL 加载失败**
   - 安装 Visual C++ 运行时库

### 10. 高级配置

#### 自定义 Hook
创建 `hook-模块名.py` 文件来处理特殊模块

#### 多平台打包
```bash
# Windows
pyinstaller --target-arch=x86_64 main.py

# 32位兼容
pyinstaller --target-arch=x86 main.py
```

#### 代码签名
```bash
# 添加数字签名（需要证书）
pyinstaller --codesign-identity="证书名称" main.py
```

## 总结

使用提供的脚本和配置文件，您可以轻松地将视频水印工具打包成可执行文件。建议先使用 `build.bat` 进行快速打包，如果遇到问题再根据错误信息进行调整。
