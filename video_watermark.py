import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import os
from threading import Thread
import sys
from moviepy.editor import VideoFileClip
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import time
from moviepy.config import change_settings
from concurrent.futures import ThreadPoolExecutor, wait
import asyncio
from functools import partial
import numpy as np
from PIL import ImageFont, Image, ImageDraw
import configparser
# 检查必要的依赖
def check_dependencies():
    missing_deps = []
    try:
        import moviepy.editor
    except ImportError:
        missing_deps.append("moviepy==1.0.3")
    
    if missing_deps:
        msg = "缺少必要的依赖库，请使用以下命令安装：\n\n"
        msg += "pip install " + " ".join(missing_deps)
        messagebox.showerror("依赖错误", msg)
        return False
    return True

class VideoWatermark:
    def __init__(self, parent=None):  # 添加 parent 参数
        # 检查依赖
        if not check_dependencies():
            sys.exit(1)
        
        if parent:
            # 如果有父窗口，使用 Toplevel
            self.root = ttk.Toplevel(parent)
            self.root.title("视频水印添加工具")
            self.root.geometry("800x900")
        else:
            # 独立运行时使用 Window
            self.root = ttk.Window(
                title="视频水印添加工具",
                themename="darkly",
                size=(800, 900)
            )
        
        # 创建变量并设置默认值
        self.video_path = tk.StringVar()
        self.watermark_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.speed = tk.IntVar()
        self.watermark_width = tk.IntVar()
        self.watermark_height = tk.IntVar()
        self.transparent = tk.DoubleVar()
        self.font_size = tk.IntVar()
        self.text_color = tk.StringVar()
        self.text_transparent = tk.DoubleVar()
        self.text_direction = tk.StringVar()
        self.text_position = tk.StringVar()
        
        # 设置默认值
        self.speed.set(3)
        self.watermark_width.set(200)
        self.watermark_height.set(200)
        self.transparent.set(0.3)
        self.font_size.set(20)
        self.text_color.set("#FFFFFF")
        self.text_transparent.set(0.9)
        self.text_direction.set("horizontal")
        self.text_position.set("bottom-center")
        
        self.setup_ui()
        
        # 设置默认文本水印内容
        default_text = "活动优惠详情以实际到店结果为主\n芜湖中诺口腔医院\n（芜）医广【2025】第06-12-65号"
        self.text_input.insert("1.0", default_text)

    def setup_ui(self):
        # 移除自定义样式设置，使用 ttkbootstrap 的主题
        
        # 主容器
        main_frame = ttk.Frame(self.root, padding="20 20 20 20")
        main_frame.pack(fill="both", expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="视频水印处理工具", 
            font=("微软雅黑", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 创建 Notebook 用于切换图片水印和文本水印
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill="x", padx=5, pady=5)
        
        # 图片水印标签页
        image_frame = ttk.Frame(notebook)
        notebook.add(image_frame, text="图片水印")
        
        # 文本水印标签页
        text_frame = ttk.Frame(notebook)
        notebook.add(text_frame, text="文本水印")
        
        # 添加去除水印标签页
        remove_frame = ttk.Frame(notebook)
        notebook.add(remove_frame, text="去除水印")
        
        # === 图片水印设置 ===
        # 水印选择
        watermark_frame = ttk.Frame(image_frame)
        watermark_frame.pack(fill="x", pady=5)
        ttk.Label(watermark_frame, text="选择水印:").pack(side="left")
        ttk.Entry(watermark_frame, textvariable=self.watermark_path).pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(watermark_frame, text="浏览", command=self.select_watermark, bootstyle="info-outline").pack(side="left")
        
        # 图片水印参数设置
        img_param_frame = ttk.LabelFrame(image_frame, text="图片水印设置", padding="15")
        img_param_frame.pack(fill="x", padx=5, pady=5)
        
        # 水印大小设置
        size_frame = ttk.Frame(img_param_frame)
        size_frame.pack(fill="x", pady=5)
        ttk.Label(size_frame, text="水印尺寸:").pack(side="left")
        ttk.Entry(size_frame, textvariable=self.watermark_width, width=8).pack(side="left", padx=(5,0))
        ttk.Label(size_frame, text="×").pack(side="left", padx=2)
        ttk.Entry(size_frame, textvariable=self.watermark_height, width=8).pack(side="left")
        
        # 速度设置
        speed_frame = ttk.Frame(img_param_frame)
        speed_frame.pack(fill="x", pady=10)
        ttk.Label(speed_frame, text="移动速度:").pack(side="left")
        ttk.Scale(speed_frame, from_=1, to=10, variable=self.speed, orient="horizontal").pack(side="left", fill="x", expand=True, padx=5)
        
        # 透明度设置
        trans_frame = ttk.Frame(img_param_frame)
        trans_frame.pack(fill="x", pady=5)
        ttk.Label(trans_frame, text="透明度:  ").pack(side="left")
        ttk.Scale(trans_frame, from_=0.1, to=1.0, variable=self.transparent, orient="horizontal").pack(side="left", fill="x", expand=True, padx=5)
        
        # === 文本水印设置 ===
        # 文本输入
        text_input_frame = ttk.Frame(text_frame)
        text_input_frame.pack(fill="both", expand=True, pady=5)
        ttk.Label(text_input_frame, text="水印文本:").pack(side="top", anchor="w")
        self.text_input = tk.Text(text_input_frame, height=4, width=40)
        self.text_input.pack(fill="both", expand=True, pady=5)
        
        # 文本参数设置
        text_param_frame = ttk.LabelFrame(text_frame, text="文本设置", padding="15")
        text_param_frame.pack(fill="x", padx=5, pady=5)
        
        # 文字大小
        font_size_frame = ttk.Frame(text_param_frame)
        font_size_frame.pack(fill="x", pady=5)
        ttk.Label(font_size_frame, text="字体大小:").pack(side="left")
        ttk.Entry(font_size_frame, textvariable=self.font_size, width=8).pack(side="left", padx=5)
        
        # 文字颜色
        color_frame = ttk.Frame(text_param_frame)
        color_frame.pack(fill="x", pady=5)
        ttk.Label(color_frame, text="文字颜色:").pack(side="left")
        ttk.Entry(color_frame, textvariable=self.text_color, width=8).pack(side="left", padx=5)
        ttk.Button(color_frame, text="选择颜色", command=self.choose_color, bootstyle="info-outline").pack(side="left", padx=5)
        
        # 文字透明度
        text_trans_frame = ttk.Frame(text_param_frame)
        text_trans_frame.pack(fill="x", pady=5)
        ttk.Label(text_trans_frame, text="透明度:").pack(side="left")
        ttk.Scale(text_trans_frame, from_=0.1, to=1.0, variable=self.text_transparent, orient="horizontal").pack(side="left", fill="x", expand=True, padx=5)
        
        # 文字方向
        direction_frame = ttk.Frame(text_param_frame)
        direction_frame.pack(fill="x", pady=5)
        ttk.Label(direction_frame, text="文字方向:").pack(side="left")
        ttk.Radiobutton(direction_frame, text="水平", variable=self.text_direction, value="horizontal").pack(side="left", padx=5)
        ttk.Radiobutton(direction_frame, text="垂直", variable=self.text_direction, value="vertical").pack(side="left", padx=5)
        
        # 文字位置
        position_frame = ttk.LabelFrame(text_param_frame, text="文字位置设置", padding="10")
        position_frame.pack(fill="x", pady=5)
        
        # 位置选择
        pos_select_frame = ttk.Frame(position_frame)
        pos_select_frame.pack(fill="x", pady=5)
        ttk.Label(pos_select_frame, text="位置类型:").pack(side="left")
        self.text_position = tk.StringVar(value="bottom-center")
        positions = [
            ("底部居中", "bottom-center"),
            ("左侧居中", "left-center"),
            ("右侧居中", "right-center")
        ]
        for text, value in positions:
            ttk.Radiobutton(pos_select_frame, text=text, variable=self.text_position, 
                           value=value, command=self.update_position_controls).pack(side="left", padx=5)
        
        # 位置比例设置框架
        self.position_ratio_frame = ttk.Frame(position_frame)
        self.position_ratio_frame.pack(fill="x", pady=5)
        
        # 高度比例（用于底部居中）
        self.height_ratio_frame = ttk.Frame(self.position_ratio_frame)
        ttk.Label(self.height_ratio_frame, text="视频高度 1/").pack(side="left")
        self.height_ratio = tk.IntVar(value=10)
        ttk.Entry(self.height_ratio_frame, textvariable=self.height_ratio, width=5).pack(side="left", padx=5)
        ttk.Label(self.height_ratio_frame, text="位置").pack(side="left")
        
        # 宽度比例（用于底部左侧和右侧）
        self.width_ratio_frame = ttk.Frame(self.position_ratio_frame)
        ttk.Label(self.width_ratio_frame, text="视频宽度 1/").pack(side="left")
        self.width_ratio = tk.IntVar(value=10)
        ttk.Entry(self.width_ratio_frame, textvariable=self.width_ratio, width=5).pack(side="left", padx=5)
        ttk.Label(self.width_ratio_frame, text="位置").pack(side="left")
        
        # 初始化显示正确的控件
        self.update_position_controls()
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="15")
        file_frame.pack(fill="x", padx=5, pady=5)
        
        # 视频选择
        video_frame = ttk.Frame(file_frame)
        video_frame.pack(fill="x", pady=5)
        ttk.Label(video_frame, text="选择视频:").pack(side="left")
        self.video_entry = ttk.Entry(video_frame, textvariable=self.video_path)
        self.video_entry.pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(video_frame, text="浏览", command=self.select_video, bootstyle="info-outline").pack(side="left")
        
        # 添加文件数量显示标签
        self.file_count_label = ttk.Label(video_frame, text="")
        self.file_count_label.pack(side="left", padx=5)
        
        # 输出路径选择
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill="x", pady=5)
        ttk.Label(output_frame, text="输出位置:").pack(side="left")
        ttk.Entry(output_frame, textvariable=self.output_path).pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(output_frame, text="浏览", command=self.select_output, bootstyle="info-outline").pack(side="left")
        
        # 参数设置区域
        param_frame = ttk.LabelFrame(main_frame, text="参数设置", padding="15")
        param_frame.pack(fill="x", padx=5, pady=15)
        
        # 开始按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(pady=20)
        start_btn = ttk.Button(
            btn_frame, 
            text="开始处理", 
            command=self.start_process, 
            bootstyle="success",
            width=20
        )
        start_btn.pack(pady=10)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(
            self.root, 
            textvariable=self.status_var,
            relief="sunken",
            font=("微软雅黑", 9)
        )
        self.status_label.pack(side="bottom", fill="x", padx=10, pady=5)

        # === 去除水印设置 ===
        # 视频选择区域
        remove_file_frame = ttk.Frame(remove_frame)
        remove_file_frame.pack(fill="x", pady=5)
        
        # 视频选择
        remove_video_frame = ttk.Frame(remove_file_frame)
        remove_video_frame.pack(fill="x", pady=5)
        ttk.Label(remove_video_frame, text="选择视频:").pack(side="left")
        self.remove_video_path = tk.StringVar()
        self.remove_video_entry = ttk.Entry(remove_video_frame, textvariable=self.remove_video_path)
        self.remove_video_entry.pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(remove_video_frame, text="浏览", command=self.select_remove_video, bootstyle="info-outline").pack(side="left")
        
        # 添加文件数量显示标签
        self.remove_file_count_label = ttk.Label(remove_video_frame, text="")
        self.remove_file_count_label.pack(side="left", padx=5)
        
        # 输出路径选择
        remove_output_frame = ttk.Frame(remove_file_frame)
        remove_output_frame.pack(fill="x", pady=5)
        ttk.Label(remove_output_frame, text="输出位置:").pack(side="left")
        self.remove_output_path = tk.StringVar()
        ttk.Entry(remove_output_frame, textvariable=self.remove_output_path).pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(remove_output_frame, text="浏览", command=self.select_remove_output, bootstyle="info-outline").pack(side="left")
        
        # 按钮区域
        remove_btn_frame = ttk.Frame(remove_frame)
        remove_btn_frame.pack(pady=20)
        
        # 去除文本水印按钮
        remove_text_btn = ttk.Button(
            remove_btn_frame,
            text="去除文本水印",
            command=self.start_remove_text_watermark,
            bootstyle="success",
            width=20
        )
        remove_text_btn.pack(pady=5)
        
        # 进度条
        self.remove_progress_var = tk.DoubleVar()
        self.remove_progress_bar = ttk.Progressbar(
            remove_frame,
            variable=self.remove_progress_var,
            maximum=100,
            bootstyle="success-striped"
        )
        self.remove_progress_bar.pack(fill="x", padx=10, pady=5)

    def select_video(self):
        filenames = filedialog.askopenfilenames(filetypes=[("视频文件", "*.mp4")])
        if filenames:
            self.video_path.set(";".join(filenames))  # 使用分号连接多个文件路径
            self.file_count_label.config(text=f"已选择 {len(filenames)} 个文件")

    def select_watermark(self):
        filename = filedialog.askopenfilename(filetypes=[("PNG文件", "*.png")])
        if filename:
            self.watermark_path.set(filename)

    def select_output(self):
        folder = filedialog.askdirectory()
        if folder:
            self.output_path.set(folder)

    async def add_watermark(self):
        # 获取所有视频文件路径
        video_paths = self.video_path.get().split(";")
        total_videos = len(video_paths)
        
        # 创建进度显示
        progress_frame = ttk.Frame(self.root)
        progress_frame.pack(side="bottom", fill="x", padx=10, pady=5)
        
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            progress_frame,
            variable=progress_var,
            maximum=total_videos,
            bootstyle="success-striped"
        )
        progress_bar.pack(fill="x")
        
        async def process_video(video_path, video_index):
            temp_video_path = None
            video = None
            output_video = None
            original_video = None
            watermarked_video = None
            final_video = None
            success = False
            
            try:
                # 更新状态
                self.status_var.set(f"正在处理: {os.path.basename(video_path)} ({video_index + 1}/{total_videos})")
                self.root.update()
                
                # 检查文件是否存在
                if not os.path.exists(video_path):
                    raise FileNotFoundError("视频文件不存在")
                if not os.path.exists(self.watermark_path.get()):
                    raise FileNotFoundError("水印图片不存在")
                
                # 修改临时文件路径，使用视频索引避免冲突
                temp_video_path = os.path.join(
                    self.output_path.get(), 
                    f"temp_{video_index}_{int(time.time())}.mp4"
                )
                temp_audio_path = os.path.join(
                    self.output_path.get(), 
                    f"temp_audio_{video_index}_{int(time.time())}.m4a"
                )
                
                video = cv2.VideoCapture(video_path)
                if not video.isOpened():
                    raise Exception("无法打开视频文件")
                
                # 获取视频信息
                frame_width = int(video.get(cv2.CAP_PROP_FRAME_WIDTH))
                frame_height = int(video.get(cv2.CAP_PROP_FRAME_HEIGHT))
                fps = video.get(cv2.CAP_PROP_FPS)
                
                # 创建输出视频
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                output_video = cv2.VideoWriter(
                    temp_video_path, 
                    fourcc, 
                    fps, 
                    (frame_width, frame_height), 
                    isColor=True
                )
                
                # 读取图片水印
                watermark = None
                if self.watermark_path.get():
                    watermark = cv2.imread(self.watermark_path.get(), cv2.IMREAD_UNCHANGED)
                    if watermark is None:
                        raise Exception("无法读取水印图片")
                    # 调整水印大小
                    watermark = cv2.resize(watermark, (self.watermark_width.get(), self.watermark_height.get()))
                
                # 准备文本水印
                text = self.text_input.get("1.0", tk.END).strip()
                font = None
                text_width = text_height = 0
                
                if text:
                    try:
                        font = ImageFont.truetype("msyh.ttc", self.font_size.get())
                    except:
                        try:
                            font = ImageFont.truetype("simhei.ttf", self.font_size.get())
                        except:
                            font = ImageFont.load_default()
                    
                    # 获取文本大小
                    text_bbox = font.getbbox(text)
                    text_width = text_bbox[2] - text_bbox[0]
                    text_height = text_bbox[3] - text_bbox[1]
                
                # 图片水印的初始位置和速度
                x_offset, y_offset = 20, 20
                x_speed = y_speed = self.speed.get()
                
                # 处理每一帧
                while True:
                    ret, frame = video.read()
                    if not ret:
                        break
                    
                    # 添加图片水印
                    if watermark is not None:
                        # 更新水印位置
                        y_offset += y_speed
                        x_offset += x_speed
                        
                        # 边界检查
                        if y_offset + watermark.shape[0] > frame_height or y_offset < 0:
                            y_speed *= -1
                            y_offset = max(0, min(y_offset, frame_height - watermark.shape[0]))
                        if x_offset + watermark.shape[1] > frame_width or x_offset < 0:
                            x_speed *= -1
                            x_offset = max(0, min(x_offset, frame_width - watermark.shape[1]))
                        
                        # 添加水印
                        y1, y2 = int(y_offset), int(y_offset + watermark.shape[0])
                        x1, x2 = int(x_offset), int(x_offset + watermark.shape[1])
                        
                        if watermark.shape[2] == 4:  # 带透明通道
                            alpha_s = watermark[:, :, 3] / 255.0 * self.transparent.get()
                            alpha_l = 1.0 - alpha_s
                            
                            for c in range(0, 3):
                                frame[y1:y2, x1:x2, c] = (alpha_s * watermark[:, :, c] +
                                                         alpha_l * frame[y1:y2, x1:x2, c])
                    
                    # 添加文本水印
                    if text:
                        # 分割多行文本
                        lines = text.split('\n')
                        
                        # 创建 PIL Image 对象
                        pil_image = Image.fromarray(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
                        draw = ImageDraw.Draw(pil_image)
                        
                        # 计算基准y坐标
                        ratio = self.height_ratio.get()
                        base_y = frame_height - frame_height // ratio
                        
                        # 计算所有行的总高度
                        line_spacing = 10  # 行间距
                        total_height = 0
                        for line in lines:
                            bbox = font.getbbox(line)
                            total_height += (bbox[3] - bbox[1]) + line_spacing
                        
                        # 调整起始y坐标，考虑总高度
                        current_y = base_y - total_height
                        
                        # 逐行绘制文本，每行居中
                        for line in lines:
                            # 获取当前行的宽度
                            bbox = font.getbbox(line)
                            line_width = bbox[2] - bbox[0]
                            
                            # 计算当前行的x坐标（居中）
                            x = (frame_width - line_width) // 2
                            
                            # 绘制当前行
                            draw.text((x, current_y), line, font=font, fill=self.text_color.get())
                            
                            # 更新y坐标到下一行
                            current_y += (bbox[3] - bbox[1]) + line_spacing
                        
                        # 转回 OpenCV 格式
                        frame = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
                    
                    # 写入输出视频
                    output_video.write(frame)
                
                # 释放OpenCV资源
                if video is not None:
                    video.release()
                if output_video is not None:
                    output_video.release()
                cv2.destroyAllWindows()
                
                # 使用moviepy合并视频和音频
                self.status_var.set("正在合并音频...")
                self.root.update()
                
                try:
                    # 获取程序运行目录
                    if getattr(sys, 'frozen', False):
                        # 如果是打包后的程序
                        application_path = os.path.dirname(sys.executable)
                    else:
                        # 如果是开发环境
                        application_path = os.path.dirname(os.path.abspath(__file__))
                        
                    # 设置 ImageMagick 路径
                    imagemagick_path = os.path.join(application_path, 'ImageMagick-7.0.10-Q16', 'magick.exe')
                    if os.path.exists(imagemagick_path):
                        # 设置 ImageMagick 的可执行文件路径,用于 moviepy 处理视频时调用 ImageMagick 进行图像处理
                        change_settings({"IMAGEMAGICK_BINARY": imagemagick_path})
                    
                    # 读取原始视频的音频
                    original_video = VideoFileClip(video_path)
                    if original_video.audio is None:
                        # 如果原视频没有音频，直接重命名临时文件为最终文件
                        if os.path.exists(temp_video_path):
                            final_output_path = os.path.join(self.output_path.get(), "watermarked_" + os.path.basename(video_path))
                            if os.path.exists(final_output_path):
                                os.remove(final_output_path)
                            os.rename(temp_video_path, final_output_path)
                            return True
                    
                    # 读取添加了水印的视频
                    watermarked_video = VideoFileClip(temp_video_path)
                    if watermarked_video is None:
                        raise Exception("无法读取处理后的视频文件")
                        
                    # 设置音频
                    final_video = watermarked_video.set_audio(original_video.audio)
                    if final_video is None:
                        raise Exception("合并音频失败")
                        
                    # 保存最终视频
                    final_output_path = os.path.join(self.output_path.get(), "watermarked_" + os.path.basename(video_path))
                    if os.path.exists(final_output_path):
                        os.remove(final_output_path)
                        
                    final_video.write_videofile(
                        final_output_path,
                        codec='libx264',
                        audio_codec='aac',
                        temp_audiofile=temp_audio_path,  # 使用唯一的临时音频文件路径
                        remove_temp=True,
                        verbose=False,
                        logger=None
                    )
                    
                except Exception as e:
                    raise Exception(f"音频处理失败: {str(e)}")
                
                finally:
                    # 确保关闭所有moviepy的视频对象
                    try:
                        if original_video is not None:
                            original_video.close()
                        if watermarked_video is not None:
                            watermarked_video.close()
                        if final_video is not None:
                            final_video.close()
                    except Exception as e:
                        print(f"关闭视频对象时出错: {e}")
                    
                    # 等待一段时间确保文件句柄被释放
                    time.sleep(1)
                    
                    # 删除临时文件
                    for temp_file in [temp_video_path, temp_audio_path]:
                        if temp_file and os.path.exists(temp_file):
                            try:
                                os.remove(temp_file)
                            except Exception as e:
                                print(f"删除临时文件失败: {e}")

                # 在成功完成所有处理后设置成功标志
                success = True
                
            except Exception as e:
                # 记录错误但不显示消息框（避免多次弹窗）
                print(f"处理视频 {os.path.basename(video_path)} 时出错：{str(e)}")
                self.status_var.set(f"处理失败: {os.path.basename(video_path)}")
            finally:
                # 清理资源
                if video is not None:
                    video.release()
                if output_video is not None:
                    output_video.release()
                cv2.destroyAllWindows()
                
                # 关闭 moviepy 对象
                for clip in [original_video, watermarked_video, final_video]:
                    if clip is not None:
                        try:
                            clip.close()
                        except:
                            pass
                
                # 删除临时文件
                for temp_file in [temp_video_path, temp_audio_path]:
                    if temp_file and os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except:
                            pass
                
                # 更新进度
                progress_var.set(video_index + 1)
                self.root.update()
            return success  # 返回处理结果

        try:
            # 创建所有任务
            tasks = [process_video(path, idx) for idx, path in enumerate(video_paths)]
            
            # 并发执行所有任务
            results = await asyncio.gather(*tasks)
            
            # 统计结果
            success_count = sum(1 for r in results if r)
            failed_count = len(results) - success_count
            
            # 显示结果
            if failed_count == 0:
                self.status_var.set("所有视频处理完成！")
                messagebox.showinfo("完成", f"已成功处理 {success_count} 个视频！")
            else:
                self.status_var.set(f"处理完成 (成功: {success_count}, 失败: {failed_count})")
                messagebox.showwarning("完成", 
                    f"处理完成\n成功: {success_count} 个\n失败: {failed_count} 个")
        
        except Exception as e:
            messagebox.showerror("错误", f"批量处理过程中出现错误：{str(e)}")
            self.status_var.set("批量处理失败")
        finally:
            progress_frame.destroy()

    def start_process(self):
        if not self.video_path.get() or not self.output_path.get():
            messagebox.showwarning("警告", "请选择视频文件和输出路径")
            return
        
        # 启动异步处理
        async def run_async():
            await self.add_watermark()
        
        def start_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(run_async())
            finally:
                loop.close()
        
        Thread(target=start_async, daemon=True).start()

    def run(self):
        self.root.mainloop()

    def choose_color(self):
        from tkinter import colorchooser
        color = colorchooser.askcolor(title="选择文字颜色", color=self.text_color.get())
        if color[1]:
            self.text_color.set(color[1])

    def update_position_controls(self):
        """根据选择的位置类型显示对应的控件"""
        # 先隐藏所有控件
        self.height_ratio_frame.pack_forget()
        self.width_ratio_frame.pack_forget()
        
        # 根据选择显示对应控件
        if self.text_position.get() == "bottom-center":
            self.height_ratio_frame.pack(side="left", padx=5)
        else:  # bottom-left 或 bottom-right
            self.width_ratio_frame.pack(side="left", padx=5)

    def get_text_position(self, frame_width, frame_height, text_size):
        """计算文本位置
        Args:
            frame_width: 视频帧宽度
            frame_height: 视频帧高度
            text_size: (文本宽度, 文本高度)
        Returns:
            (x, y): 文本位置坐标
        """
        text_width, text_height = text_size
        position = self.text_position.get()
        
        if position == "bottom-center":
            # 底部居中，使用高度比例
            ratio = self.height_ratio.get()
            y = frame_height - frame_height // ratio - text_height - 20  # 底部留出20像素边距
            x = (frame_width - text_width) // 2  # 水平居中
        elif position == "left-center":
            # 底部左侧，使用宽度比例
            ratio = self.width_ratio.get()
            x = frame_width // ratio
            y = frame_height - text_height - 20  # 底部留出20像素边距
        else:  # bottom-right
            # 底部右侧，使用宽度比例
            ratio = self.width_ratio.get()
            x = frame_width - frame_width // ratio - text_width
            y = frame_height - text_height - 20  # 底部留出20像素边距
        
        return int(x), int(y)

    def select_remove_video(self):
        filenames = filedialog.askopenfilenames(filetypes=[("视频文件", "*.mp4")])
        if filenames:
            self.remove_video_path.set(";".join(filenames))
            self.remove_file_count_label.config(text=f"已选择 {len(filenames)} 个文件")

    def select_remove_output(self):
        folder = filedialog.askdirectory()
        if folder:
            self.remove_output_path.set(folder)

    async def remove_watermark(self):
        try:
            from delete_watermark import WatermarkRemover
            
            # 获取所有视频文件路径
            video_paths = self.remove_video_path.get().split(";")
            total_videos = len(video_paths)
            
            for idx, video_path in enumerate(video_paths):
                try:
                    # 更新状态
                    self.status_var.set(f"正在处理: {os.path.basename(video_path)} ({idx + 1}/{total_videos})")
                    self.root.update()
                    
                    # 创建输出文件路径
                    output_path = os.path.join(
                        self.remove_output_path.get(),
                        f"removed_watermark_{os.path.basename(video_path)}"
                    )
                    
                    # 创建去水印处理器实例
                    remover = WatermarkRemover()
                    
                    # 处理视频
                    remover.process_video(video_path, output_path)
                    
                    # 更新进度
                    self.remove_progress_var.set((idx + 1) / total_videos * 100)
                    self.root.update()
                    
                except Exception as e:
                    print(f"处理视频 {os.path.basename(video_path)} 时出错：{str(e)}")
                    self.status_var.set(f"处理失败: {os.path.basename(video_path)}")
            
            messagebox.showinfo("完成", "水印去除完成！")
            self.status_var.set("水印去除完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"去除水印过程中出现错误：{str(e)}")
            self.status_var.set("去除水印失败")

    def start_remove_watermark(self):
        if not self.remove_video_path.get() or not self.remove_output_path.get():
            messagebox.showwarning("警告", "请选择视频文件和输出路径")
            return
        
        # 启动异步处理
        async def run_async():
            await self.remove_watermark()
        
        def start_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(run_async())
            finally:
                loop.close()
        
        Thread(target=start_async, daemon=True).start()

    def update_progress(self, progress):
        """更新进度条的方法"""
        self.remove_progress_var.set(progress)
        self.root.update()

    async def remove_text_watermark(self):
        try:
            from delete_watermark import WatermarkRemover
            from PIL import ImageFont
            
            # 获取所有视频文件路径
            video_paths = self.remove_video_path.get().split(";")
            total_videos = len(video_paths)
            
            for idx, video_path in enumerate(video_paths):
                try:
                    # 更新状态
                    self.status_var.set(f"请在视频窗口中框选要去除的水印区域，按Enter键确认")
                    self.root.update()
                    
                    # 创建输出文件路径
                    output_path = os.path.join(
                        self.remove_output_path.get(),
                        f"removed_text_{os.path.basename(video_path)}"
                    )
                    
                    # 创建去水印处理器实例
                    remover = WatermarkRemover()
                    
                    # 显示视频第一帧用于选择区域
                    remover.select_roi(video_path)
                    
                    # 更新状态
                    self.status_var.set(f"正在处理: {os.path.basename(video_path)} ({idx + 1}/{total_videos})")
                    self.root.update()
                    
                    # 重置进度条
                    self.remove_progress_var.set(0)
                    
                    def progress_callback(frame_idx, total_frames):
                        # 使用 after 方法确保在主线程中更新 UI
                        progress = (frame_idx / total_frames) * 100
                        self.root.after(0, lambda: self.update_progress(progress))
                    
                    # 处理视频，传入进度回调函数
                    remover.process_video(video_path, output_path, progress_callback)
                    
                    # 更新总体进度
                    total_progress = ((idx + 1) / total_videos) * 100
                    self.remove_progress_var.set(total_progress)
                    self.root.update()
                    
                except Exception as e:
                    print(f"处理视频 {os.path.basename(video_path)} 时出错：{str(e)}")
                    self.status_var.set(f"去除文本水印失败: {os.path.basename(video_path)}")
            
            messagebox.showinfo("完成", "文本水印去除完成！")
            self.status_var.set("文本水印去除完成")
            
        except Exception as e:
            messagebox.showerror("错误", f"去除文本水印过程中出现错误：{str(e)}")
            self.status_var.set("去除文本水印失败")

    def start_remove_text_watermark(self):
        if not self.remove_video_path.get() or not self.remove_output_path.get():
            messagebox.showwarning("警告", "请选择视频文件和输出路径")
            return
        
        # 启动异步处理
        async def run_async():
            await self.remove_text_watermark()
        
        def start_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(run_async())
            finally:
                loop.close()
        
        Thread(target=start_async, daemon=True).start()

if __name__ == "__main__":
    app = VideoWatermark()
    app.run() 