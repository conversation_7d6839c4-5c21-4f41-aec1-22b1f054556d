@echo off
chcp 65001 >nul
title 修复 numpy 导入问题的打包

echo ========================================
echo 修复 numpy 导入问题的打包脚本
echo ========================================
echo.

REM 检查 Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未找到 Python
    pause
    exit /b 1
)
echo [✓] Python 已安装

REM 检查 PyInstaller
python -c "import PyInstaller" >nul 2>&1
if %errorlevel% neq 0 (
    echo [信息] 安装 PyInstaller...
    pip install pyinstaller
)
echo [✓] PyInstaller 已准备

REM 清理旧文件
echo [信息] 清理旧文件...
if exist dist rmdir /s /q dist >nul 2>&1
if exist build rmdir /s /q build >nul 2>&1
echo [✓] 清理完成

echo.
echo [信息] 开始打包（目录模式，推荐）...
echo.

REM 目录打包（推荐，解决 numpy 问题）
pyinstaller --windowed ^
    --name="视频水印工具" ^
    --add-data="config.ini;." ^
    --collect-submodules=numpy ^
    --collect-submodules=cv2 ^
    --hidden-import=numpy.core._multiarray_tests ^
    --hidden-import=numpy.core._multiarray_umath ^
    --hidden-import=numpy.core.multiarray ^
    --hidden-import=numpy.core.umath ^
    --hidden-import=cv2.cv2 ^
    --hidden-import=moviepy ^
    --hidden-import=ttkbootstrap ^
    --hidden-import=PIL ^
    --hidden-import=tqdm ^
    --hidden-import=wmi ^
    --hidden-import=configparser ^
    --hookspath=. ^
    video_watermark.py

REM 检查结果
if exist "dist\视频水印工具\视频水印工具.exe" (
    echo.
    echo ========================================
    echo [成功] 目录打包完成！
    echo ========================================
    echo 可执行文件: dist\视频水印工具\视频水印工具.exe
    echo 整个 dist\视频水印工具\ 目录都需要一起分发
    echo.
    
    set /p test="是否测试运行？(y/n): "
    if /i "!test!"=="y" (
        echo [信息] 启动程序...
        start "" "dist\视频水印工具\视频水印工具.exe"
    )
    
    echo.
    set /p single="是否尝试单文件打包？(y/n): "
    if /i "!single!"=="y" (
        echo.
        echo [信息] 开始单文件打包...
        
        pyinstaller --onefile --windowed ^
            --name="视频水印工具_单文件" ^
            --add-data="config.ini;." ^
            --collect-submodules=numpy ^
            --collect-submodules=cv2 ^
            --hidden-import=numpy.core._multiarray_tests ^
            --hidden-import=cv2.cv2 ^
            --hidden-import=moviepy ^
            --hidden-import=ttkbootstrap ^
            --exclude-module=matplotlib ^
            --exclude-module=scipy ^
            --exclude-module=pandas ^
            --hookspath=. ^
            video_watermark.py
        
        if exist "dist\视频水印工具_单文件.exe" (
            echo [成功] 单文件打包也完成了！
            echo 单文件版本: dist\视频水印工具_单文件.exe
        ) else (
            echo [失败] 单文件打包失败，但目录版本可用
        )
    )
    
) else (
    echo.
    echo ========================================
    echo [失败] 打包失败！
    echo ========================================
    echo 可能的解决方案：
    echo 1. 运行: python fix_numpy_build.py
    echo 2. 检查是否缺少依赖包
    echo 3. 尝试在虚拟环境中打包
    echo.
    echo 安装依赖命令：
    echo pip install moviepy opencv-python ttkbootstrap pillow numpy tqdm wmi
)

echo.
echo ========================================
echo 打包说明
echo ========================================
echo 目录打包优点：
echo - 启动速度快
echo - 兼容性好
echo - 容易调试
echo.
echo 单文件打包优点：
echo - 分发方便
echo - 只有一个文件
echo.
echo 注意：目录打包需要整个文件夹一起分发！
echo.

pause
