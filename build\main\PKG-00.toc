('F:\\soft\\python\\workdata\\vidieo_mark\\build\\main\\main.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'F:\\soft\\python\\workdata\\vidieo_mark\\build\\main\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'F:\\soft\\python\\workdata\\vidieo_mark\\build\\main\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'F:\\soft\\python\\workdata\\vidieo_mark\\build\\main\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'F:\\soft\\python\\workdata\\vidieo_mark\\build\\main\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'F:\\soft\\python\\workdata\\vidieo_mark\\build\\main\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'F:\\soft\\python\\workdata\\vidieo_mark\\build\\main\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pywintypes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pywintypes.py',
   'PYSOURCE'),
  ('pyi_rth_pythoncom',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_pythoncom.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('main', 'F:\\soft\\python\\workdata\\vidieo_mark\\main.py', 'PYSOURCE'),
  ('ffmpeg\\ffmpeg.exe', 'F:\\soft\\ffmpeg\\bin\\ffmpeg.exe', 'BINARY'),
  ('imageio_ffmpeg\\binaries\\ffmpeg-win-x86_64-v7.1.exe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\binaries\\ffmpeg-win-x86_64-v7.1.exe',
   'BINARY'),
  ('python39.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\python39.dll',
   'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-caad452230ae4ddb57899b8b3a33c55c.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-caad452230ae4ddb57899b8b3a33c55c.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-23ebcc0b37c8e3d074511f362feac48b.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy.libs\\msvcp140-23ebcc0b37c8e3d074511f362feac48b.dll',
   'BINARY'),
  ('cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\opencv_videoio_ffmpeg4110_64.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes39.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pywin32_system32\\pywintypes39.dll',
   'BINARY'),
  ('pywin32_system32\\pythoncom39.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\pywin32_system32\\pythoncom39.dll',
   'BINARY'),
  ('select.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_webp.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('PIL\\_imagingft.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_imagingft.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_imagingtk.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_imagingcms.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_imagingmath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\_cffi_backend.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\PIL\\_imaging.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\md__mypyc.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\charset_normalizer\\md.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\mtrand.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_sfc64.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_philox.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_pcg64.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_mt19937.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\bit_generator.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_generator.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\random\\_common.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp39-win_amd64.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp39-win_amd64.pyd',
   'EXTENSION'),
  ('cv2\\cv2.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\cv2.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('win32\\_win32sysloader.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('Pythonwin\\win32ui.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('win32\\win32trace.pyd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('python3.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\python3.dll',
   'BINARY'),
  ('Pythonwin\\mfc140u.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('tk86t.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\tk86t.dll',
   'BINARY'),
  ('tcl86t.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\DLLs\\tcl86t.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'F:\\Program Files\\anaconda\\envs\\watermark\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('base_library.zip',
   'F:\\soft\\python\\workdata\\vidieo_mark\\build\\main\\base_library.zip',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\LICENSE',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\WHEEL',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\METADATA',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\RECORD',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\importlib_metadata-8.5.0.dist-info\\INSTALLER',
   'DATA'),
  ('numpy.libs\\.load-order-numpy-2.0.2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\numpy.libs\\.load-order-numpy-2.0.2',
   'DATA'),
  ('imageio_ffmpeg\\binaries\\README.md',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\imageio_ffmpeg\\binaries\\README.md',
   'DATA'),
  ('cv2\\config.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\config.py',
   'DATA'),
  ('cv2\\load_config_py3.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\load_config_py3.py',
   'DATA'),
  ('cv2\\config-3.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\config-3.py',
   'DATA'),
  ('certifi\\cacert.pem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('certifi\\py.typed',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tk_data\\text.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.18.tm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8\\8.4\\platform-1.0.18.tm',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tk_data\\button.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\East-Saskatchewan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\East-Saskatchewan',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tk_data\\license.terms',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific-New',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Pacific-New',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tk_data\\images\\README',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tk_data\\console.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.5.tm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8\\8.6\\http-2.9.5.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tk_data\\tclIndex',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.3.tm',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8\\8.5\\tcltest-2.5.3.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'F:\\Program Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('wheel-0.44.0.dist-info\\WHEEL',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel-0.44.0.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.44.0.dist-info\\METADATA',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel-0.44.0.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.44.0.dist-info\\LICENSE.txt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel-0.44.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.44.0.dist-info\\RECORD',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel-0.44.0.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.44.0.dist-info\\entry_points.txt',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\wheel-0.44.0.dist-info\\entry_points.txt',
   'DATA'),
  ('cv2\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\__init__.py',
   'DATA'),
  ('cv2\\version.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\version.py',
   'DATA'),
  ('cv2\\utils\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\utils\\__init__.py',
   'DATA'),
  ('cv2\\typing\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\typing\\__init__.py',
   'DATA'),
  ('cv2\\misc\\version.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\misc\\version.py',
   'DATA'),
  ('cv2\\misc\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\misc\\__init__.py',
   'DATA'),
  ('cv2\\mat_wrapper\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\mat_wrapper\\__init__.py',
   'DATA'),
  ('cv2\\gapi\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\gapi\\__init__.py',
   'DATA'),
  ('cv2\\data\\__init__.py',
   'F:\\Program '
   'Files\\anaconda\\envs\\watermark\\lib\\site-packages\\cv2\\data\\__init__.py',
   'DATA')],
 'python39.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
