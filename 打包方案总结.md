# 视频水印工具 PyInstaller 打包方案总结

## 🎯 推荐方案（按难度排序）

### 1. 🟢 最简单方案
**适合**: 新手用户，快速打包

**步骤**:
1. 双击运行 `快速打包.bat`
2. 等待完成

**命令**:
```bash
pyinstaller --onefile --windowed --name="视频水印工具" --add-data="config.ini;." main.py
```

### 2. 🟡 标准方案
**适合**: 有一定经验的用户

**步骤**:
1. 安装依赖: `pip install pyinstaller`
2. 运行: `python simple_build.py`

**特点**: 自动处理常见问题

### 3. 🔴 高级方案
**适合**: 需要自定义配置的用户

**步骤**:
1. 编辑 `video_watermark_tool.spec`
2. 运行: `pyinstaller video_watermark_tool.spec`

**特点**: 完全可控，支持复杂配置

## 📁 提供的文件说明

| 文件名 | 类型 | 用途 | 推荐度 |
|--------|------|------|--------|
| `快速打包.bat` | 批处理 | 一键打包，最简单 | ⭐⭐⭐⭐⭐ |
| `simple_build.py` | Python脚本 | 简化打包脚本 | ⭐⭐⭐⭐ |
| `build_exe.py` | Python脚本 | 完整打包脚本 | ⭐⭐⭐ |
| `video_watermark_tool.spec` | Spec文件 | 高级配置文件 | ⭐⭐ |
| `build.bat` | 批处理 | 标准批处理脚本 | ⭐⭐⭐ |
| `requirements.txt` | 依赖列表 | 项目依赖 | ⭐⭐⭐⭐⭐ |

## 🚀 快速开始指南

### 方法1: 一键打包（推荐）
```bash
# 1. 双击运行
快速打包.bat

# 2. 等待完成，程序会自动：
#    - 检查 Python 环境
#    - 安装 PyInstaller
#    - 清理旧文件
#    - 执行打包
#    - 显示结果
```

### 方法2: 命令行打包
```bash
# 1. 安装 PyInstaller
pip install pyinstaller

# 2. 基础打包
pyinstaller --onefile --windowed main.py

# 3. 完整打包（推荐）
pyinstaller --onefile --windowed --name="视频水印工具" \
    --add-data="config.ini;." \
    --hidden-import=moviepy \
    --hidden-import=cv2 \
    --hidden-import=ttkbootstrap \
    main.py
```

### 方法3: 使用 Python 脚本
```bash
# 简单脚本
python simple_build.py

# 完整脚本
python build_exe.py
```

## 🔧 常见问题及解决方案

### 问题1: 缺少依赖包
**错误**: `ModuleNotFoundError: No module named 'xxx'`

**解决方案**:
```bash
# 安装基础依赖
pip install moviepy opencv-python ttkbootstrap pillow numpy tqdm wmi

# 或使用 requirements.txt
pip install -r requirements.txt
```

### 问题2: 配置文件找不到
**错误**: 程序运行时找不到 `config.ini`

**解决方案**: 确保打包命令包含
```bash
--add-data="config.ini;."
```

### 问题3: FFmpeg 相关错误
**错误**: `ffmpeg not found`

**解决方案**:
1. 下载 FFmpeg 到项目目录
2. 修改 spec 文件添加 FFmpeg 路径
3. 或在打包命令中添加:
```bash
--add-binary="ffmpeg.exe;."
```

### 问题4: 文件过大
**现象**: exe 文件超过 100MB

**解决方案**:
1. 使用虚拟环境打包
2. 排除不需要的模块:
```bash
--exclude-module=matplotlib --exclude-module=scipy
```

### 问题5: 启动慢
**现象**: 双击后等待很久才启动

**解决方案**: 使用目录打包而非单文件
```bash
pyinstaller --windowed main.py  # 去掉 --onefile
```

## 📋 打包检查清单

### 打包前检查
- [ ] Python 环境正常
- [ ] 所有依赖已安装
- [ ] 程序能正常运行
- [ ] config.ini 文件存在
- [ ] FFmpeg 路径正确（如需要）

### 打包后检查
- [ ] exe 文件生成成功
- [ ] 文件大小合理（通常 50-200MB）
- [ ] 配置文件已包含
- [ ] 在干净系统上测试运行
- [ ] 所有功能正常工作

### 分发前检查
- [ ] 创建使用说明
- [ ] 准备示例文件
- [ ] 测试不同 Windows 版本
- [ ] 考虑数字签名

## 🎯 推荐工作流程

### 开发阶段
1. 使用 `快速打包.bat` 进行快速测试
2. 遇到问题时查看错误信息
3. 根据需要调整隐藏导入

### 发布阶段
1. 使用完整的打包命令
2. 在多台机器上测试
3. 创建安装包（可选）
4. 准备分发材料

## 📊 性能对比

| 打包方式 | 文件大小 | 启动速度 | 分发便利性 | 推荐场景 |
|----------|----------|----------|------------|----------|
| 单文件 | 大 | 慢 | 高 | 简单分发 |
| 目录 | 中 | 快 | 中 | 专业部署 |
| 安装包 | 小 | 快 | 高 | 商业发布 |

## 🔗 相关资源

- [PyInstaller 官方文档](https://pyinstaller.readthedocs.io/)
- [Python 打包指南](https://packaging.python.org/)
- [FFmpeg 下载](https://ffmpeg.org/download.html)

## 📞 技术支持

如果遇到打包问题：
1. 查看本文档的常见问题部分
2. 检查 PyInstaller 日志输出
3. 在干净的虚拟环境中重试
4. 考虑使用不同的打包参数

---

**总结**: 对于大多数用户，推荐直接使用 `快速打包.bat`，它会自动处理大部分常见情况。如果需要更多控制，可以使用提供的 Python 脚本或手动调整 spec 文件。
