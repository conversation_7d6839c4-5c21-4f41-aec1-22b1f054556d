#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 numpy 导入问题的打包脚本
"""

import os
import subprocess
import sys
import shutil

def clean_build():
    """清理构建文件"""
    print("清理构建文件...")
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 删除 {dir_name}")

def build_with_numpy_fix():
    """使用修复 numpy 问题的方式打包"""
    print("=" * 60)
    print("修复 numpy 导入问题的打包")
    print("=" * 60)
    
    # 清理旧文件
    clean_build()
    
    # 方法1: 使用目录打包（推荐）
    print("尝试目录打包（启动更快，兼容性更好）...")
    
    cmd1 = [
        'pyinstaller',
        '--windowed',  # 去掉 --onefile，使用目录打包
        '--name=视频水印工具',
        '--add-data=config.ini;.',
        '--collect-submodules=numpy',
        '--collect-submodules=cv2',
        '--hidden-import=numpy.core._multiarray_tests',
        '--hidden-import=numpy.core._multiarray_umath',
        '--hidden-import=numpy.core.multiarray',
        '--hidden-import=numpy.core.umath',
        '--hidden-import=cv2.cv2',
        '--hidden-import=moviepy',
        '--hidden-import=ttkbootstrap',
        '--hidden-import=PIL',
        '--hidden-import=tqdm',
        '--hidden-import=wmi',
        '--hidden-import=configparser',
        '--hookspath=.',  # 使用当前目录的 hook 文件
        'video_watermark.py'
    ]
    
    try:
        print("执行命令:")
        print(' '.join(cmd1))
        print()
        
        result = subprocess.run(cmd1, check=True, capture_output=True, text=True)
        print("✓ 目录打包成功！")
        
        # 检查结果
        exe_path = os.path.join('dist', '视频水印工具', '视频水印工具.exe')
        if os.path.exists(exe_path):
            print(f"✓ 可执行文件: {exe_path}")
            return True
        
    except subprocess.CalledProcessError as e:
        print("✗ 目录打包失败")
        print("错误信息:")
        print(e.stderr)
    
    # 方法2: 单文件打包（备选）
    print("\n尝试单文件打包...")
    
    cmd2 = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=视频水印工具_单文件',
        '--add-data=config.ini;.',
        '--collect-submodules=numpy',
        '--collect-submodules=cv2',
        '--hidden-import=numpy.core._multiarray_tests',
        '--hidden-import=numpy.core._multiarray_umath',
        '--hidden-import=cv2.cv2',
        '--hidden-import=moviepy',
        '--hidden-import=ttkbootstrap',
        '--exclude-module=matplotlib',
        '--exclude-module=scipy',
        '--exclude-module=pandas',
        '--hookspath=.',
        'video_watermark.py'
    ]
    
    try:
        print("执行命令:")
        print(' '.join(cmd2))
        print()
        
        result = subprocess.run(cmd2, check=True, capture_output=True, text=True)
        print("✓ 单文件打包成功！")
        
        # 检查结果
        exe_path = os.path.join('dist', '视频水印工具_单文件.exe')
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"✓ 可执行文件: {exe_path}")
            print(f"✓ 文件大小: {size_mb:.1f} MB")
            return True
        
    except subprocess.CalledProcessError as e:
        print("✗ 单文件打包失败")
        print("错误信息:")
        print(e.stderr)
    
    return False

def build_minimal():
    """最小化打包（如果其他方法都失败）"""
    print("\n尝试最小化打包...")
    
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=视频水印工具_最小',
        '--add-data=config.ini;.',
        '--exclude-module=torch',
        '--exclude-module=tensorboard',
        '--exclude-module=ultralytics',
        '--exclude-module=matplotlib',
        '--exclude-module=scipy',
        '--exclude-module=pandas',
        'video_watermark.py'
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 最小化打包成功！")
        return True
    except subprocess.CalledProcessError as e:
        print("✗ 最小化打包失败")
        print(e.stderr)
        return False

def main():
    """主函数"""
    print("修复 numpy 导入问题的打包脚本")
    print("=" * 60)
    
    # 检查 PyInstaller
    try:
        import PyInstaller
        print("✓ PyInstaller 已安装")
    except ImportError:
        print("安装 PyInstaller...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
    
    # 尝试不同的打包方法
    success = build_with_numpy_fix()
    
    if not success:
        print("\n常规方法失败，尝试最小化打包...")
        success = build_minimal()
    
    if success:
        print("\n" + "=" * 60)
        print("打包完成！")
        print("=" * 60)
        
        # 显示所有生成的文件
        if os.path.exists('dist'):
            print("生成的文件:")
            for item in os.listdir('dist'):
                item_path = os.path.join('dist', item)
                if os.path.isfile(item_path):
                    size_mb = os.path.getsize(item_path) / (1024 * 1024)
                    print(f"  📁 {item} ({size_mb:.1f} MB)")
                elif os.path.isdir(item_path):
                    print(f"  📂 {item}/ (目录)")
        
        print("\n使用建议:")
        print("1. 优先使用目录版本（启动更快）")
        print("2. 单文件版本便于分发")
        print("3. 在目标机器上测试运行")
        
    else:
        print("\n" + "=" * 60)
        print("所有打包方法都失败了")
        print("=" * 60)
        print("建议:")
        print("1. 检查 Python 环境和依赖")
        print("2. 尝试在虚拟环境中打包")
        print("3. 手动安装缺失的模块")
        print("4. 查看详细错误信息")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
