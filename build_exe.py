#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频水印工具打包脚本
使用 PyInstaller 将 Python 项目打包成可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """检查必要的依赖"""
    print("检查依赖...")
    
    required_packages = [
        'pyinstaller',
        'moviepy',
        'opencv-python',
        'ttkbootstrap',
        'pillow',
        'numpy',
        'tqdm',
        'wmi',
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} (缺失)")
    
    if missing_packages:
        print(f"\n缺失的包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def clean_build():
    """清理之前的构建文件"""
    print("清理构建文件...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"✓ 删除 {dir_name}")
    
    # 删除 .pyc 文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))

def create_requirements_txt():
    """创建 requirements.txt 文件"""
    requirements = [
        "moviepy==1.0.3",
        "opencv-python>=4.5.0",
        "ttkbootstrap>=1.10.0",
        "Pillow>=8.0.0",
        "numpy>=1.20.0",
        "tqdm>=4.60.0",
        "WMI>=1.5.0",
        "torch>=1.9.0",
        "ultralytics",
        "ffmpeg-python",
        "configparser",
    ]
    
    with open('requirements.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(requirements))
    
    print("✓ 创建 requirements.txt")

def build_with_pyinstaller():
    """使用 PyInstaller 构建"""
    print("开始 PyInstaller 构建...")
    
    # 构建命令
    cmd = [
        'pyinstaller',
        '--clean',  # 清理缓存
        '--noconfirm',  # 不询问覆盖
        'video_watermark_tool.spec'
    ]
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ PyInstaller 构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ PyInstaller 构建失败:")
        print(f"错误输出: {e.stderr}")
        return False

def build_simple():
    """简单构建方式（如果 spec 文件有问题）"""
    print("使用简单构建方式...")
    
    cmd = [
        'pyinstaller',
        '--onefile',  # 单文件
        '--windowed',  # 无控制台
        '--name=视频水印工具',
        '--add-data=config.ini;.',  # 添加配置文件
        '--hidden-import=moviepy',
        '--hidden-import=cv2',
        '--hidden-import=ttkbootstrap',
        '--hidden-import=PIL',
        '--hidden-import=numpy',
        '--hidden-import=tqdm',
        '--hidden-import=wmi',
        '--hidden-import=configparser',
        'main.py'
    ]
    
    # 如果有 FFmpeg，添加它
    ffmpeg_paths = [
        'F:\\soft\\ffmpeg\\bin\\ffmpeg.exe',
        'C:\\ffmpeg\\bin\\ffmpeg.exe',
        'ffmpeg.exe',
    ]
    
    for ffmpeg_path in ffmpeg_paths:
        if os.path.exists(ffmpeg_path):
            cmd.extend(['--add-binary', f'{ffmpeg_path};ffmpeg'])
            break
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✓ 简单构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 简单构建失败:")
        print(f"错误输出: {e.stderr}")
        return False

def copy_additional_files():
    """复制额外的文件到 dist 目录"""
    print("复制额外文件...")
    
    dist_dir = Path('dist')
    if not dist_dir.exists():
        print("✗ dist 目录不存在")
        return
    
    files_to_copy = [
        'config.ini',
        'README.md',
        '优化总结.md',
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, dist_dir)
            print(f"✓ 复制 {file_name}")

def create_installer_script():
    """创建安装脚本"""
    installer_content = '''@echo off
echo 视频水印工具安装程序
echo.

REM 检查是否有管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限
) else (
    echo 请以管理员身份运行此脚本
    pause
    exit /b 1
)

REM 创建程序目录
set INSTALL_DIR=C:\\Program Files\\VideoWatermarkTool
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM 复制文件
copy "视频水印工具.exe" "%INSTALL_DIR%\\"
copy "config.ini" "%INSTALL_DIR%\\"

REM 创建桌面快捷方式
set DESKTOP=%USERPROFILE%\\Desktop
echo [InternetShortcut] > "%DESKTOP%\\视频水印工具.lnk"
echo URL=file:///%INSTALL_DIR%\\视频水印工具.exe >> "%DESKTOP%\\视频水印工具.lnk"

echo 安装完成！
echo 程序已安装到: %INSTALL_DIR%
echo 桌面快捷方式已创建
pause
'''
    
    with open('dist/install.bat', 'w', encoding='gbk') as f:
        f.write(installer_content)
    
    print("✓ 创建安装脚本")

def main():
    """主函数"""
    print("=" * 60)
    print("视频水印工具 PyInstaller 打包脚本")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 创建 requirements.txt
    create_requirements_txt()
    
    # 清理构建文件
    clean_build()
    
    # 尝试使用 spec 文件构建
    success = build_with_pyinstaller()
    
    # 如果失败，尝试简单构建
    if not success:
        print("尝试简单构建方式...")
        success = build_simple()
    
    if success:
        # 复制额外文件
        copy_additional_files()
        
        # 创建安装脚本
        create_installer_script()
        
        print("\n" + "=" * 60)
        print("构建完成！")
        print("=" * 60)
        print("可执行文件位置: dist/视频水印工具.exe")
        print("配置文件: dist/config.ini")
        print("安装脚本: dist/install.bat")
        print("\n使用说明:")
        print("1. 直接运行 dist/视频水印工具.exe")
        print("2. 或者运行 dist/install.bat 进行系统安装")
        
        return True
    else:
        print("\n" + "=" * 60)
        print("构建失败！")
        print("=" * 60)
        print("请检查错误信息并解决依赖问题")
        return False

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
