import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from video_watermark import VideoWatermark
import os
from ttkbootstrap import Style
import threading
import hashlib
import wmi
import json
class LicenseValidator:
    def __init__(self):
        self.root = tk.Tk()
        # ttkbootstrap 内置主题:
        # cosmo, flatly, litera, minty, lumen, sandstone, 
        # yeti, pulse, united, morph, journal, darkly,
        # superhero, solar, cyborg, vapor, simplex, cerculean
        self.style = Style(theme="darkly")
        self.root.title('软件授权验证')
        self.root.geometry('500x400')
        
        # 检查是否已授权
        if self.check_license():
            self.root.withdraw()  # 隐藏授权窗口
            self.start_main_program()
            return
            
        # 如果未授权，显示授权界面
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 500) // 2
        y = (screen_height - 400) // 2
        self.root.geometry(f'500x400+{x}+{y}')
        
        self.setup_ui()
    
    def check_license(self):
        """检查本地授权信息"""
        try:
            if os.path.exists('license.json'):
                with open('license.json', 'r') as f:
                    license_data = json.load(f)
                    
                # 获取当前机器码
                current_machine_code = self.get_machine_code_str()
                
                # 验证机器码和注册码是否匹配
                if (license_data.get('machine_code') == current_machine_code and 
                    license_data.get('license_key') == self.generate_license_key(current_machine_code)):
                    return True
            return False
        except Exception:
            return False
    
    def save_license(self, machine_code, license_key):
        """保存授权信息到本地"""
        license_data = {
            'machine_code': machine_code,
            'license_key': license_key
        }
        with open('license.json', 'w') as f:
            json.dump(license_data, f)
    
    def get_machine_code_str(self):
        """获取机器码字符串"""
        try:
            c = wmi.WMI()
            cpu = c.Win32_Processor()[0].ProcessorId.strip()
            board = c.Win32_BaseBoard()[0].SerialNumber.strip()
            machine_code = f"{cpu}-{board}"
            return hashlib.sha256(machine_code.encode()).hexdigest()
        except Exception:
            return ""
    
    def get_machine_code(self):
        """获取机器码按钮的回调函数"""
        try:
            machine_code = self.get_machine_code_str()
            self.machine_code_entry.delete(0, tk.END)
            self.machine_code_entry.insert(0, machine_code)
        except Exception as e:
            messagebox.showerror("错误", f"获取机器码失败: {str(e)}")
    
    def validate_license(self):
        machine_code = self.machine_code_entry.get().strip()
        license_key = self.license_entry.get().strip()
        
        if not machine_code or not license_key:
            messagebox.showerror("错误", "请先获取机器码并输入注册码！")
            return
            
        # 验证注册码
        expected_license = self.generate_license_key(machine_code)
        if license_key == expected_license:
            # 保存授权信息
            self.save_license(machine_code, license_key)
            messagebox.showinfo("成功", "授权验证通过！")
            self.root.withdraw()
            self.start_main_program()
        else:
            messagebox.showerror("错误", "注册码验证失败！")
    
    def start_main_program(self):
        """启动主程序"""
        try:
        # 添加 ffmpeg 配置
            import moviepy.config as conf
            import sys
            import os
            
            # 获取程序运行路径
            if getattr(sys, 'frozen', False):
                # 如果是打包后的可执行文件
                application_path = sys._MEIPASS
            else:
                # 如果是直接运行的 Python 脚本
                application_path = os.path.dirname(os.path.abspath(__file__))
                
            # 设置 ffmpeg 路径
            ffmpeg_path = os.path.join(application_path, 'ffmpeg', 'ffmpeg.exe')
            if os.path.exists(ffmpeg_path):
                conf.change_settings({"FFMPEG_BINARY": ffmpeg_path})
                
            app = VideoWatermark(parent=self.root)
            app.root.protocol("WM_DELETE_WINDOW", lambda: self.on_closing(app))
            app.run()
        except Exception as e:
            self.root.deiconify()
            messagebox.showerror("错误", f"启动主程序失败: {str(e)}")

    
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20")
        # 使主框架填充整个窗口空间,BOTH表示水平和垂直方向都填充,expand=True允许框架随窗口大小调整而扩展
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        title_label = ttk.Label(
            title_frame, 
            text="软件授权验证系统", 
            font=('Arial', 16, 'bold'),
            foreground='#00BFFF'
        )
        title_label.pack()
        
        # 创建内容框架
        content_frame = ttk.LabelFrame(
            main_frame, 
            text="授权信息", 
            padding="15",
            style='info.TLabelframe'
        )
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 机器码显示框
        code_frame = ttk.Frame(content_frame)
        code_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(
            code_frame, 
            text="机器码:",
            font=('Arial', 10)
        ).pack(side=tk.LEFT, padx=5)
        
        self.machine_code_entry = ttk.Entry(
            code_frame, 
            width=50,
            style='info.TEntry'
        )
        self.machine_code_entry.pack(side=tk.LEFT, padx=5)
        
        # 获取机器码按钮
        ttk.Button(
            content_frame,
            text="获取机器码",
            command=self.get_machine_code,
            style='info.TButton',
            width=20
        ).pack(pady=15)
        
        # 分隔线
        ttk.Separator(content_frame, orient='horizontal').pack(fill=tk.X, pady=15)
        
        # 注册码输入框
        license_frame = ttk.Frame(content_frame)
        license_frame.pack(fill=tk.X, pady=10)
        
        ttk.Label(
            license_frame, 
            text="注册码:",
            font=('Arial', 10)
        ).pack(side=tk.LEFT, padx=5)
        
        self.license_entry = ttk.Entry(
            license_frame, 
            width=50,
            style='info.TEntry'
        )
        self.license_entry.pack(side=tk.LEFT, padx=5)
        
        # 验证按钮
        ttk.Button(
            content_frame,
            text="验证授权",
            command=self.validate_license,
            style='info.TButton',
            width=20
        ).pack(pady=15)
    
    def generate_license_key(self, machine_code):
        # 将机器码和密钥组合
        combined = f"{machine_code}zhujunyong"
        # 使用SHA256进行加密
        return hashlib.sha256(combined.encode()).hexdigest()
    
    def on_closing(self, app):
        """处理主窗口关闭事件"""
        app.root.destroy()
        self.root.destroy()  # 同时关闭授权窗口
    
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    # 修改入口，先启动授权验证界面
    app = LicenseValidator()
    app.run() 