# -*- mode: python ; coding: utf-8 -*-
"""
视频水印工具 PyInstaller 配置文件
优化版本，包含所有必要的依赖和资源
"""

import os
import sys

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(SPEC))

# 数据文件列表
datas = [
    # 配置文件
    (os.path.join(current_dir, 'config.ini'), '.'),
    # 如果有图标文件
    # (os.path.join(current_dir, 'icon.ico'), '.'),
]

# FFmpeg 路径 - 根据实际情况调整
ffmpeg_paths = [
    'F:\\soft\\ffmpeg\\bin\\ffmpeg.exe',  # 原路径
    'C:\\ffmpeg\\bin\\ffmpeg.exe',        # 常见路径1
    'ffmpeg.exe',                         # 当前目录
]

# 查找可用的 FFmpeg
ffmpeg_found = False
for ffmpeg_path in ffmpeg_paths:
    if os.path.exists(ffmpeg_path):
        datas.append((ffmpeg_path, 'ffmpeg'))
        ffmpeg_found = True
        print(f"找到 FFmpeg: {ffmpeg_path}")
        break

if not ffmpeg_found:
    print("警告: 未找到 FFmpeg，请手动添加到 datas 中")

# 隐藏导入列表
hiddenimports = [
    # 核心依赖
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',

    # 视频处理
    'moviepy',
    'moviepy.editor',
    'moviepy.config',
    'cv2',

    # NumPy 相关（修复导入问题）
    'numpy',
    'numpy.core',
    'numpy.core._multiarray_tests',
    'numpy.core._multiarray_umath',
    'numpy.core.multiarray',
    'numpy.core.umath',
    'numpy.linalg',
    'numpy.linalg.lapack_lite',
    'numpy.linalg._umath_linalg',
    'numpy.random',
    'numpy.random.common',
    'numpy.random.bounded_integers',
    'numpy.random.entropy',

    # 图像处理
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',

    # OpenCV 相关
    'cv2.cv2',

    # 音视频编解码
    'imageio',
    'imageio_ffmpeg',
    'ffmpeg',
    'ffmpeg-python',

    # UI 框架
    'ttkbootstrap',
    'ttkbootstrap.constants',

    # 深度学习（如果使用）
    'torch',
    'torch.utils.tensorboard',
    'tensorboard',
    'tensorboard.program',

    # 系统相关
    'wmi',
    'hashlib',
    'json',
    'configparser',
    'threading',
    'asyncio',
    'concurrent.futures',

    # 进度条
    'tqdm',

    # 其他可能需要的模块
    'functools',
    'time',
    'os',
    'sys',
]

# 排除的模块（减少打包大小）
excludes = [
    'matplotlib',
    'scipy',
    'pandas',
    'jupyter',
    'IPython',
    'notebook',
    'pytest',
    'unittest',
]

a = Analysis(
    [os.path.join(current_dir, 'video_watermark.py')],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[current_dir],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=0,
)

# 过滤掉不需要的文件
def filter_binaries(binaries):
    """过滤二进制文件，移除不需要的大文件"""
    filtered = []
    exclude_patterns = [
        'Qt5',  # 如果不使用 Qt
        'PySide',
        'PyQt',
        'test',
        'tests',
    ]
    
    for binary in binaries:
        name = binary[0].lower()
        if not any(pattern.lower() in name for pattern in exclude_patterns):
            filtered.append(binary)
    
    return filtered

a.binaries = filter_binaries(a.binaries)

pyz = PYZ(a.pure, a.zipped_data)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='视频水印工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 压缩可执行文件
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    # icon=os.path.join(current_dir, 'icon.ico'),  # 如果有图标文件
)
