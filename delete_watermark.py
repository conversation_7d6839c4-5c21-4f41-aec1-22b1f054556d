#pip install ultralytics
#pip install lama-cleaner
import cv2
import numpy as np
import torch
from moviepy.editor import VideoFileClip
import ffmpeg
from tqdm import tqdm
import tensorboard
import tensorboard.program

class WatermarkRemover:
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"使用设备: {self.device}")
        self.watermark_roi = None
        self.watermark_mask = None
        # 预计算的遮罩，避免每帧重复计算
        self.dilated_mask = None
        self.blend_mask = None
        
    def select_roi(self, video_path):
        """让用户手动选择水印区域"""
        # 这里是显示视频的第一帧用于选择水印区域
        cap = cv2.VideoCapture(video_path)
        ret, frame = cap.read()
        if not ret:
            raise ValueError("无法读取视频帧")
            
        # 调整显示大小为720p
        display_height = 720
        scale_factor = display_height / frame.shape[0]
        display_width = int(frame.shape[1] * scale_factor)
        display_frame = cv2.resize(frame, (display_width, display_height))
        
        # 显示选择说明
        instructions = "选择水印区域，然后按空格或回车确认"
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(display_frame, instructions, (10, 30), font, 1, (255, 255, 255), 2, cv2.LINE_AA)
        
        # 用户选择ROI - 这里会显示视频帧让用户选择区域
        roi = cv2.selectROI(display_frame)
        cv2.destroyAllWindows()
        
        # 转换回原始分辨率
        self.watermark_roi = (
            int(roi[0] / scale_factor),
            int(roi[1] / scale_factor),
            int(roi[2] / scale_factor),
            int(roi[3] / scale_factor)
        )
        cap.release()
        
    def generate_watermark_mask(self, video_path, num_frames=5, min_frame_count=3):
        """生成水印遮罩"""
        if self.watermark_roi is None:
            self.select_roi(video_path)
            
        video_clip = VideoFileClip(video_path)
        total_frames = int(video_clip.duration * video_clip.fps)
        # 选择更分散的帧以获得更好的样本
        frame_indices = [int(i * total_frames / num_frames) for i in range(num_frames)]
        
        # 收集多帧进行分析
        masks = []
        for idx in frame_indices:
            frame = video_clip.get_frame(idx / video_clip.fps)
            mask = self._detect_watermark_adaptive(frame)
            masks.append(mask)
        
        # 使用更严格的合并策略
        final_mask = sum((mask == 255).astype(np.uint8) for mask in masks)
        self.watermark_mask = np.where(final_mask >= min_frame_count, 255, 0).astype(np.uint8)
        
        # 改进遮罩处理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        # 先进行开运算去除小噪点
        self.watermark_mask = cv2.morphologyEx(self.watermark_mask, cv2.MORPH_OPEN, kernel)
        # 再进行闭运算填充小孔
        self.watermark_mask = cv2.morphologyEx(self.watermark_mask, cv2.MORPH_CLOSE, kernel)
        # 最后轻微膨胀
        self.watermark_mask = cv2.dilate(self.watermark_mask, kernel, iterations=1)

        # 预计算膨胀遮罩，避免每帧重复计算
        self.dilated_mask = cv2.dilate(
            self.watermark_mask,
            cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5)),
            iterations=2
        )

        # 预计算混合遮罩
        x, y, w, h = self.watermark_roi
        roi_mask = self.watermark_mask[y:y+h, x:x+w]
        self.blend_mask = cv2.GaussianBlur(roi_mask, (31, 31), 0).astype(float) / 255

        video_clip.close()
        
    def _detect_watermark_adaptive(self, frame):
        """使用改进的方法检测带描边的水印"""
        x, y, w, h = self.watermark_roi
        roi_frame = frame[y:y+h, x:x+w]
        
        # 转换为YUV颜色空间
        yuv_frame = cv2.cvtColor(roi_frame, cv2.COLOR_RGB2YUV)
        y_channel = yuv_frame[:,:,0]
        
        # 应用CLAHE增强对比度
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(y_channel)
        
        # 使用双边滤波
        filtered = cv2.bilateralFilter(enhanced, 9, 75, 75)
        
        # 使用自适应阈值处理
        binary_frame = cv2.adaptiveThreshold(
            filtered,
            255,
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY_INV,
            25,  # 块大小
            7    # 常数值
        )
        
        # 使用形态学操作清理噪点
        kernel_clean = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary_frame = cv2.morphologyEx(binary_frame, cv2.MORPH_OPEN, kernel_clean)
        
        # 多尺度边缘检测
        edges = np.zeros_like(filtered)
        for sigma in [0.5, 1.0, 2.0]:
            blurred = cv2.GaussianBlur(filtered, (0, 0), sigma)
            edge = cv2.Canny(blurred, 20, 150)
            edges = cv2.bitwise_or(edges, edge)
        
        # 扩展边缘
        kernel_dilate = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        edges = cv2.dilate(edges, kernel_dilate, iterations=2)
        
        # 合并处理结果
        combined_mask = cv2.bitwise_or(binary_frame, edges)
        
        # 使用形态学闭操作连接组件
        kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (9, 9))
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel_close)
        
        # 创建完整的遮罩
        mask = np.zeros(frame.shape[:2], dtype=np.uint8)
        mask[y:y+h, x:x+w] = combined_mask
        
        return mask
        
    def process_video(self, input_path, output_path, progress_callback=None, fast_mode=True):
        """处理视频 - 优化版本
        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            progress_callback: 进度回调函数，接收当前帧索引和总帧数作为参数
            fast_mode: 是否使用快速模式（默认True，牺牲一些质量换取速度）
        """
        if self.watermark_mask is None:
            print("生成水印遮罩...")
            self.generate_watermark_mask(input_path)

        print("开始处理视频...")

        # 使用 OpenCV 直接处理视频，避免 MoviePy 的开销
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            raise ValueError("无法打开视频文件")

        # 获取视频属性
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        # 创建临时视频文件
        temp_video_path = "temp_no_watermark.mp4"
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_video_path, fourcc, fps, (width, height))

        # 创建进度条
        progress_bar = tqdm(total=total_frames, desc="处理进度", unit="帧")

        # 创建进度计数器
        current_frame = 0
        
        # 优化的帧处理函数
        def process_frame_optimized(frame):
            """优化的帧处理函数，减少重复计算"""
            if fast_mode:
                # 快速模式：只使用基本的修复算法
                result = cv2.inpaint(frame, self.dilated_mask, 3, cv2.INPAINT_NS)
            else:
                # 质量模式：使用更好的算法但速度较慢
                result = cv2.inpaint(frame, self.dilated_mask, 5, cv2.INPAINT_TELEA)

                x, y, w, h = self.watermark_roi
                roi = result[y:y+h, x:x+w]

                # 使用预计算的混合遮罩
                if self.blend_mask.max() > 0.1:
                    smoothed_roi = cv2.GaussianBlur(roi, (5, 5), 1)

                    # 混合原始区域和平滑区域
                    result[y:y+h, x:x+w] = (
                        roi * (1 - self.blend_mask[:,:,np.newaxis]) +
                        smoothed_roi * self.blend_mask[:,:,np.newaxis]
                    )

            return result

        # 逐帧处理视频
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # 处理帧
                processed_frame = process_frame_optimized(frame)

                # 写入输出视频
                out.write(processed_frame)

                # 更新进度
                current_frame += 1
                progress_bar.update(1)
                if progress_callback:
                    progress_callback(current_frame, total_frames)

        finally:
            # 清理资源
            cap.release()
            out.release()
            progress_bar.close()

        print("\n合并音频...")
        try:
            # 使用ffmpeg合并音频和处理后的视频
            input_video = ffmpeg.input(temp_video_path)
            input_audio = ffmpeg.input(input_path).audio

            ffmpeg.output(
                input_video,
                input_audio,
                output_path,
                vcodec='libx264',  # 重新编码以确保兼容性
                acodec='aac',
                crf=23,  # 设置质量参数
                preset='medium'  # 平衡速度和质量
            ).overwrite_output().run(capture_stdout=True, capture_stderr=True)

            # 清理临时文件
            import os
            if os.path.exists(temp_video_path):
                os.remove(temp_video_path)

            print("处理完成！")

        except Exception as e:
            print(f"合并音频时出错: {str(e)}")
            # 如果合并失败，至少保留处理后的视频
            import os
            if os.path.exists(temp_video_path):
                os.rename(temp_video_path, output_path)
                print(f"已保存无音频的处理结果到: {output_path}")
            raise

if __name__ == "__main__":
    try:
        remover = WatermarkRemover()
        remover.process_video("input.mp4", "output.mp4")
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"错误: {str(e)}")