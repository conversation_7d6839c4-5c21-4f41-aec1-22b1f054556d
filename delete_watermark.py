#pip install ultralytics
#pip install lama-cleaner
import cv2
import numpy as np
import torch
from moviepy.editor import VideoFileClip
import ffmpeg
from tqdm import tqdm
import tensorboard
import tensorboard.program

class WatermarkRemover:
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"使用设备: {self.device}")
        self.watermark_roi = None
        self.watermark_mask = None
        
    def select_roi(self, video_path):
        """让用户手动选择水印区域"""
        # 这里是显示视频的第一帧用于选择水印区域
        cap = cv2.VideoCapture(video_path)
        ret, frame = cap.read()
        if not ret:
            raise ValueError("无法读取视频帧")
            
        # 调整显示大小为720p
        display_height = 720
        scale_factor = display_height / frame.shape[0]
        display_width = int(frame.shape[1] * scale_factor)
        display_frame = cv2.resize(frame, (display_width, display_height))
        
        # 显示选择说明
        instructions = "选择水印区域，然后按空格或回车确认"
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(display_frame, instructions, (10, 30), font, 1, (255, 255, 255), 2, cv2.LINE_AA)
        
        # 用户选择ROI - 这里会显示视频帧让用户选择区域
        roi = cv2.selectROI(display_frame)
        cv2.destroyAllWindows()
        
        # 转换回原始分辨率
        self.watermark_roi = (
            int(roi[0] / scale_factor),
            int(roi[1] / scale_factor),
            int(roi[2] / scale_factor),
            int(roi[3] / scale_factor)
        )
        cap.release()
        
    def generate_watermark_mask(self, video_path, num_frames=10, min_frame_count=7):
        """生成水印遮罩"""
        if self.watermark_roi is None:
            self.select_roi(video_path)
            
        video_clip = VideoFileClip(video_path)
        total_frames = int(video_clip.duration * video_clip.fps)
        # 选择更分散的帧以获得更好的样本
        frame_indices = [int(i * total_frames / num_frames) for i in range(num_frames)]
        
        # 收集多帧进行分析
        masks = []
        for idx in frame_indices:
            frame = video_clip.get_frame(idx / video_clip.fps)
            mask = self._detect_watermark_adaptive(frame)
            masks.append(mask)
        
        # 使用更严格的合并策略
        final_mask = sum((mask == 255).astype(np.uint8) for mask in masks)
        self.watermark_mask = np.where(final_mask >= min_frame_count, 255, 0).astype(np.uint8)
        
        # 改进遮罩处理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        # 先进行开运算去除小噪点
        self.watermark_mask = cv2.morphologyEx(self.watermark_mask, cv2.MORPH_OPEN, kernel)
        # 再进行闭运算填充小孔
        self.watermark_mask = cv2.morphologyEx(self.watermark_mask, cv2.MORPH_CLOSE, kernel)
        # 最后轻微膨胀
        self.watermark_mask = cv2.dilate(self.watermark_mask, kernel, iterations=1)
        
        video_clip.close()
        
    def _detect_watermark_adaptive(self, frame):
        """使用改进的方法检测带描边的水印"""
        x, y, w, h = self.watermark_roi
        roi_frame = frame[y:y+h, x:x+w]
        
        # 转换为YUV颜色空间
        yuv_frame = cv2.cvtColor(roi_frame, cv2.COLOR_RGB2YUV)
        y_channel = yuv_frame[:,:,0]
        
        # 应用CLAHE增强对比度
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        enhanced = clahe.apply(y_channel)
        
        # 使用双边滤波
        filtered = cv2.bilateralFilter(enhanced, 9, 75, 75)
        
        # 使用自适应阈值处理
        binary_frame = cv2.adaptiveThreshold(
            filtered,
            255,
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
            cv2.THRESH_BINARY_INV,
            25,  # 块大小
            7    # 常数值
        )
        
        # 使用形态学操作清理噪点
        kernel_clean = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        binary_frame = cv2.morphologyEx(binary_frame, cv2.MORPH_OPEN, kernel_clean)
        
        # 多尺度边缘检测
        edges = np.zeros_like(filtered)
        for sigma in [0.5, 1.0, 2.0]:
            blurred = cv2.GaussianBlur(filtered, (0, 0), sigma)
            edge = cv2.Canny(blurred, 20, 150)
            edges = cv2.bitwise_or(edges, edge)
        
        # 扩展边缘
        kernel_dilate = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        edges = cv2.dilate(edges, kernel_dilate, iterations=2)
        
        # 合并处理结果
        combined_mask = cv2.bitwise_or(binary_frame, edges)
        
        # 使用形态学闭操作连接组件
        kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (9, 9))
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel_close)
        
        # 创建完整的遮罩
        mask = np.zeros(frame.shape[:2], dtype=np.uint8)
        mask[y:y+h, x:x+w] = combined_mask
        
        return mask
        
    def process_video(self, input_path, output_path, progress_callback=None):
        """处理视频
        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            progress_callback: 进度回调函数，接收当前帧索引和总帧数作为参数
        """
        if self.watermark_mask is None:
            print("生成水印遮罩...")
            self.generate_watermark_mask(input_path)
            
        print("开始处理视频...")
        video_clip = VideoFileClip(input_path)
        total_frames = int(video_clip.duration * video_clip.fps)
        
        # 创建进度条
        progress_bar = tqdm(total=total_frames, desc="处理进度", unit="帧")
        
        # 创建进度计数器
        current_frame = 0
        
        def process_frame(frame):
            nonlocal current_frame
            # 创建更大的修复区域
            dilated_mask = cv2.dilate(
                self.watermark_mask, 
                cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5)),
                iterations=2
            )
            
            # 使用扩大的修复半径和TELEA算法
            result = cv2.inpaint(frame, dilated_mask, 9, cv2.INPAINT_TELEA)
            
            x, y, w, h = self.watermark_roi
            roi = result[y:y+h, x:x+w]
            
            # 创建更平滑的过渡
            blend_mask = cv2.GaussianBlur(self.watermark_mask[y:y+h, x:x+w], (31, 31), 0)
            blend_mask = blend_mask.astype(float) / 255
            
            # 对处理区域进行双边滤波
            smoothed_roi = cv2.bilateralFilter(roi, 9, 75, 75)
            
            # 混合原始区域和平滑区域
            result[y:y+h, x:x+w] = (
                roi * (1 - blend_mask[:,:,np.newaxis]) + 
                smoothed_roi * blend_mask[:,:,np.newaxis]
            )
            
            # 最后应用轻微的去噪
            denoised_roi = cv2.fastNlMeansDenoisingColored(
                result[y:y+h, x:x+w],
                None,
                h=5,
                hColor=5,
                templateWindowSize=7,
                searchWindowSize=21
            )
            result[y:y+h, x:x+w] = denoised_roi
            
            # 更新进度
            current_frame += 1
            progress_bar.update(1)
            if progress_callback:
                progress_callback(current_frame, total_frames)
            
            return result
            
        # 处理视频
        processed_video = video_clip.fl_image(process_frame)
        
        # 保存临时文件
        processed_video.write_videofile(
            "temp.mp4",
            codec="libx264",
            audio_codec="aac",
            temp_audiofile="temp-audio.m4a",
            remove_temp=True
        )
        
        # 清理资源
        video_clip.close()
        processed_video.close()
        progress_bar.close()
        
        print("\n合并音频...")
        try:
            # 使用ffmpeg合并
            input_video = ffmpeg.input("temp.mp4")
            input_audio = ffmpeg.input(input_path).audio
            
            ffmpeg.output(
                input_video,
                input_audio,
                output_path,
                vcodec='copy',
                acodec='aac'
            ).overwrite_output().run(capture_stdout=True, capture_stderr=True)
            
            # 清理临时文件
            import os
            if os.path.exists("temp.mp4"):
                os.remove("temp.mp4")
                
            print("处理完成！")
            
        except Exception as e:
            print(f"合并音频时出错: {str(e)}")
            raise

if __name__ == "__main__":
    try:
        remover = WatermarkRemover()
        remover.process_video("input.mp4", "output.mp4")
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"错误: {str(e)}")