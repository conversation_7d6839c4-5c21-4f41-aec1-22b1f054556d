#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本：测试去水印功能的优化效果
"""

import cv2
import numpy as np
import time
import os

def create_test_image(width=640, height=480):
    """创建测试图像"""
    # 创建一个带有模拟水印的测试图像
    image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    
    # 添加模拟水印区域（白色矩形）
    cv2.rectangle(image, (50, 50), (200, 100), (255, 255, 255), -1)
    cv2.putText(image, "WATERMARK", (60, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    
    return image

def create_test_mask(width=640, height=480):
    """创建测试遮罩"""
    mask = np.zeros((height, width), dtype=np.uint8)
    cv2.rectangle(mask, (50, 50), (200, 100), 255, -1)
    return mask

def test_original_algorithm(image, mask, iterations=100):
    """测试原始算法性能"""
    print("测试原始算法（复杂处理）...")
    
    start_time = time.time()
    
    for i in range(iterations):
        # 模拟原始算法的复杂处理
        # 1. 膨胀遮罩
        dilated_mask = cv2.dilate(
            mask, 
            cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5)),
            iterations=2
        )
        
        # 2. 修复
        result = cv2.inpaint(image, dilated_mask, 9, cv2.INPAINT_TELEA)
        
        # 3. 高斯模糊
        roi = result[50:100, 50:200]
        blend_mask = cv2.GaussianBlur(mask[50:100, 50:200], (31, 31), 0)
        blend_mask = blend_mask.astype(float) / 255
        
        # 4. 双边滤波
        smoothed_roi = cv2.bilateralFilter(roi, 9, 75, 75)
        
        # 5. 混合
        result[50:100, 50:200] = (
            roi * (1 - blend_mask[:,:,np.newaxis]) + 
            smoothed_roi * blend_mask[:,:,np.newaxis]
        )
        
        # 6. 去噪
        denoised_roi = cv2.fastNlMeansDenoisingColored(
            result[50:100, 50:200],
            None,
            h=5,
            hColor=5,
            templateWindowSize=7,
            searchWindowSize=21
        )
        result[50:100, 50:200] = denoised_roi
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / iterations
    
    print(f"原始算法 - 总时间: {total_time:.3f}秒")
    print(f"原始算法 - 平均每帧: {avg_time*1000:.2f}毫秒")
    
    return total_time, avg_time

def test_optimized_algorithm(image, mask, iterations=100):
    """测试优化算法性能"""
    print("测试优化算法（快速模式）...")
    
    # 预计算遮罩（模拟优化）
    dilated_mask = cv2.dilate(
        mask, 
        cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5)),
        iterations=2
    )
    
    start_time = time.time()
    
    for i in range(iterations):
        # 优化算法：只使用基本修复
        result = cv2.inpaint(image, dilated_mask, 3, cv2.INPAINT_NS)
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / iterations
    
    print(f"优化算法 - 总时间: {total_time:.3f}秒")
    print(f"优化算法 - 平均每帧: {avg_time*1000:.2f}毫秒")
    
    return total_time, avg_time

def test_quality_mode_algorithm(image, mask, iterations=100):
    """测试优化算法的质量模式"""
    print("测试优化算法（质量模式）...")
    
    # 预计算遮罩
    dilated_mask = cv2.dilate(
        mask, 
        cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5)),
        iterations=2
    )
    
    roi_mask = mask[50:100, 50:200]
    blend_mask = cv2.GaussianBlur(roi_mask, (31, 31), 0).astype(float) / 255
    
    start_time = time.time()
    
    for i in range(iterations):
        # 质量模式：使用预计算的遮罩
        result = cv2.inpaint(image, dilated_mask, 5, cv2.INPAINT_TELEA)
        
        roi = result[50:100, 50:200]
        
        if blend_mask.max() > 0.1:
            smoothed_roi = cv2.GaussianBlur(roi, (5, 5), 1)
            result[50:100, 50:200] = (
                roi * (1 - blend_mask[:,:,np.newaxis]) + 
                smoothed_roi * blend_mask[:,:,np.newaxis]
            )
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / iterations
    
    print(f"质量模式 - 总时间: {total_time:.3f}秒")
    print(f"质量模式 - 平均每帧: {avg_time*1000:.2f}毫秒")
    
    return total_time, avg_time

def main():
    """主测试函数"""
    print("=" * 60)
    print("去水印性能优化测试")
    print("=" * 60)
    
    # 创建测试数据
    print("创建测试数据...")
    test_image = create_test_image()
    test_mask = create_test_mask()
    
    iterations = 50  # 测试帧数
    print(f"测试帧数: {iterations}")
    print()
    
    # 测试原始算法
    original_total, original_avg = test_original_algorithm(test_image, test_mask, iterations)
    print()
    
    # 测试优化算法（快速模式）
    optimized_total, optimized_avg = test_optimized_algorithm(test_image, test_mask, iterations)
    print()
    
    # 测试优化算法（质量模式）
    quality_total, quality_avg = test_quality_mode_algorithm(test_image, test_mask, iterations)
    print()
    
    # 性能对比
    print("=" * 60)
    print("性能对比结果")
    print("=" * 60)
    
    fast_speedup = original_total / optimized_total
    quality_speedup = original_total / quality_total
    
    print(f"原始算法:     {original_avg*1000:.2f} ms/帧")
    print(f"快速模式:     {optimized_avg*1000:.2f} ms/帧 (提升 {fast_speedup:.1f}x)")
    print(f"质量模式:     {quality_avg*1000:.2f} ms/帧 (提升 {quality_speedup:.1f}x)")
    print()
    
    # 计算视频处理时间估算
    fps = 30
    duration_minutes = 1
    total_frames = fps * duration_minutes * 60
    
    print(f"处理 {duration_minutes} 分钟 30fps 视频 ({total_frames} 帧) 的预估时间:")
    print(f"原始算法:     {(original_avg * total_frames / 60):.1f} 分钟")
    print(f"快速模式:     {(optimized_avg * total_frames / 60):.1f} 分钟")
    print(f"质量模式:     {(quality_avg * total_frames / 60):.1f} 分钟")
    print()
    
    print("优化总结:")
    print(f"✓ 快速模式性能提升: {fast_speedup:.1f}倍")
    print(f"✓ 质量模式性能提升: {quality_speedup:.1f}倍")
    print(f"✓ 快速模式时间节省: {((original_total-optimized_total)/original_total*100):.1f}%")
    print(f"✓ 质量模式时间节省: {((original_total-quality_total)/original_total*100):.1f}%")

if __name__ == "__main__":
    main()
