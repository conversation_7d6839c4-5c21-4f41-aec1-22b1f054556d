# -*- coding: utf-8 -*-
"""
PyInstaller hook for numpy
解决 numpy 导入问题
"""

from PyInstaller.utils.hooks import collect_submodules, collect_data_files

# 收集所有 numpy 子模块
hiddenimports = collect_submodules('numpy')

# 添加特定的隐藏导入
hiddenimports += [
    'numpy.core._multiarray_tests',
    'numpy.core._multiarray_umath',
    'numpy.core.multiarray',
    'numpy.core.umath',
    'numpy.linalg.lapack_lite',
    'numpy.linalg._umath_linalg',
    'numpy.random.common',
    'numpy.random.bounded_integers',
    'numpy.random.entropy',
]

# 收集数据文件
datas = collect_data_files('numpy')

# 排除一些不需要的模块以减小大小
excludedimports = [
    'numpy.tests',
    'numpy.testing',
    'numpy.distutils',
    'numpy.f2py',
]
