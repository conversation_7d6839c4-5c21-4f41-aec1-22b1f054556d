#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试脚本：只测试配置文件读取功能
"""

import os
import configparser

def load_ad_code_from_config():
    """从配置文件读取广告代码"""
    try:
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        # 读取 ad_code，如果不存在则使用默认值
        if config.has_section('CODE') and config.has_option('CODE', 'ad_code'):
            ad_code = config.get('CODE', 'ad_code')
            # 处理换行符
            ad_code = ad_code.replace('\\n', '\n')
            return ad_code
        else:
            # 如果配置文件不存在或没有相应配置，返回默认值
            return "活动优惠详情以实际到店结果为主\n芜湖中诺口腔医院\n（芜）医广【2025】第06-12-65号"
    except Exception as e:
        print(f"读取配置文件失败: {str(e)}")
        # 如果读取失败，返回默认值
        return "活动优惠详情以实际到店结果为主\n芜湖中诺口腔医院\n（芜）医广【2025】第06-12-65号"

def test_config_reading():
    """测试配置文件读取功能"""
    print("=" * 50)
    print("测试配置文件读取功能")
    print("=" * 50)
    
    # 检查配置文件是否存在
    if os.path.exists('config.ini'):
        print("✓ config.ini 文件存在")
        
        # 读取原始内容
        with open('config.ini', 'r', encoding='utf-8') as f:
            content = f.read()
        print("原始文件内容：")
        print(content)
        print()
        
    else:
        print("✗ config.ini 文件不存在")
        return
    
    # 测试配置文件读取
    ad_code = load_ad_code_from_config()
    print("从配置文件读取的广告代码：")
    print(repr(ad_code))  # 使用 repr 显示转义字符
    print()
    print("显示效果：")
    print(ad_code)
    print()
    
    # 验证是否正确处理了换行符
    if '\\n' in ad_code:
        print("✗ 警告：换行符未正确处理")
    else:
        print("✓ 换行符处理正确")
    
    # 验证是否包含预期内容
    if "芜湖中诺口腔医院" in ad_code:
        print("✓ 配置文件内容读取正确")
    else:
        print("✗ 配置文件内容读取异常")
    
    # 验证换行符数量
    newline_count = ad_code.count('\n')
    print(f"✓ 检测到 {newline_count} 个换行符")

def test_config_file_creation():
    """测试配置文件的创建和读取"""
    print("=" * 50)
    print("测试配置文件创建和读取")
    print("=" * 50)
    
    # 创建测试配置文件
    test_config_path = "test_config.ini"
    test_ad_code = "测试广告内容\\n第二行内容\\n第三行内容"
    
    try:
        # 创建配置文件
        config = configparser.ConfigParser()
        config.add_section('CODE')
        config.set('CODE', 'ad_code', test_ad_code)
        
        with open(test_config_path, 'w', encoding='utf-8') as f:
            config.write(f)
        
        print(f"✓ 测试配置文件已创建：{test_config_path}")
        
        # 显示创建的文件内容
        with open(test_config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        print("创建的文件内容：")
        print(content)
        print()
        
        # 读取配置文件
        config_read = configparser.ConfigParser()
        config_read.read(test_config_path, encoding='utf-8')
        
        if config_read.has_section('CODE') and config_read.has_option('CODE', 'ad_code'):
            read_ad_code = config_read.get('CODE', 'ad_code')
            processed_ad_code = read_ad_code.replace('\\n', '\n')
            
            print(f"原始内容：{test_ad_code}")
            print(f"读取内容：{read_ad_code}")
            print(f"处理后内容：{repr(processed_ad_code)}")
            print("处理后显示效果：")
            print(processed_ad_code)
            print()
            
            if processed_ad_code.count('\n') == 2:
                print("✓ 换行符处理正确")
            else:
                print("✗ 换行符处理异常")
        else:
            print("✗ 配置文件读取失败")
            
    except Exception as e:
        print(f"✗ 配置文件测试失败：{str(e)}")
    
    finally:
        # 清理测试文件
        if os.path.exists(test_config_path):
            os.remove(test_config_path)
            print(f"✓ 测试文件已清理：{test_config_path}")

def main():
    """主测试函数"""
    print("开始测试配置文件读取功能...")
    print()
    
    # 测试现有配置文件读取
    test_config_reading()
    print()
    
    # 测试配置文件创建和读取
    test_config_file_creation()
    print()
    
    print("=" * 50)
    print("配置文件测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
