import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import os
from threading import Thread
import sys
from moviepy.editor import VideoFileClip
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import time
from moviepy.config import change_settings
from concurrent.futures import ThreadPoolExecutor, wait
import asyncio
from functools import partial

# 检查必要的依赖
def check_dependencies():
    missing_deps = []
    try:
        import moviepy.editor
    except ImportError:
        missing_deps.append("moviepy==1.0.3")
    
    if missing_deps:
        msg = "缺少必要的依赖库，请使用以下命令安装：\n\n"
        msg += "pip install " + " ".join(missing_deps)
        messagebox.showerror("依赖错误", msg)
        return False
    return True

class VideoWatermark:
    def __init__(self, parent=None):  # 添加 parent 参数
        # 检查依赖
        if not check_dependencies():
            sys.exit(1)
        
        if parent:
            # 如果有父窗口，使用 Toplevel
            self.root = ttk.Toplevel(parent)
            self.root.title("视频水印添加工具")
            self.root.geometry("800x650")
        else:
            # 独立运行时使用 Window
            self.root = ttk.Window(
                title="视频水印添加工具",
                themename="darkly",
                size=(800, 650)
            )
        
        # 创建变量并设置默认值
        self.video_path = tk.StringVar()
        self.watermark_path = tk.StringVar()
        self.output_path = tk.StringVar()
        self.speed = tk.IntVar()
        self.watermark_width = tk.IntVar()
        self.watermark_height = tk.IntVar()
        self.transparent = tk.DoubleVar()
        
        # 设置默认值
        self.speed.set(3)
        self.watermark_width.set(100)
        self.watermark_height.set(100)
        self.transparent.set(0.5)
        
        self.setup_ui()
        
    def setup_ui(self):
        # 移除自定义样式设置，使用 ttkbootstrap 的主题
        
        # 主容器
        main_frame = ttk.Frame(self.root, padding="20 20 20 20")
        main_frame.pack(fill="both", expand=True)
        
        # 标题
        title_label = ttk.Label(
            main_frame, 
            text="视频水印处理工具", 
            font=("微软雅黑", 16, "bold")
        )
        title_label.pack(pady=(0, 20))
        
        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="15")
        file_frame.pack(fill="x", padx=5, pady=5)
        
        # 视频选择
        video_frame = ttk.Frame(file_frame)
        video_frame.pack(fill="x", pady=5)
        ttk.Label(video_frame, text="选择视频:").pack(side="left")
        self.video_entry = ttk.Entry(video_frame, textvariable=self.video_path)
        self.video_entry.pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(video_frame, text="浏览", command=self.select_video, bootstyle="info-outline").pack(side="left")
        
        # 添加文件数量显示标签
        self.file_count_label = ttk.Label(video_frame, text="")
        self.file_count_label.pack(side="left", padx=5)
        
        # 水印选择
        watermark_frame = ttk.Frame(file_frame)
        watermark_frame.pack(fill="x", pady=5)
        ttk.Label(watermark_frame, text="选择水印:").pack(side="left")
        ttk.Entry(watermark_frame, textvariable=self.watermark_path).pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(watermark_frame, text="浏览", command=self.select_watermark, bootstyle="info-outline").pack(side="left")
        
        # 输出路径选择
        output_frame = ttk.Frame(file_frame)
        output_frame.pack(fill="x", pady=5)
        ttk.Label(output_frame, text="输出位置:").pack(side="left")
        ttk.Entry(output_frame, textvariable=self.output_path).pack(side="left", fill="x", expand=True, padx=5)
        ttk.Button(output_frame, text="浏览", command=self.select_output, bootstyle="info-outline").pack(side="left")
        
        # 参数设置区域
        param_frame = ttk.LabelFrame(main_frame, text="参数设置", padding="15")
        param_frame.pack(fill="x", padx=5, pady=15)
        
        # 水印大小设置
        size_frame = ttk.Frame(param_frame)
        size_frame.pack(fill="x", pady=5)
        ttk.Label(size_frame, text="水印尺寸:").pack(side="left")
        ttk.Entry(size_frame, textvariable=self.watermark_width, width=8).pack(side="left", padx=(5,0))
        print(self.watermark_width)
        print(self.watermark_width.get())
        ttk.Label(size_frame, text="×").pack(side="left", padx=2)
        ttk.Entry(size_frame, textvariable=self.watermark_height, width=8).pack(side="left")
        
        # 速度设置
        speed_frame = ttk.Frame(param_frame)
        speed_frame.pack(fill="x", pady=10)
        ttk.Label(speed_frame, text="移动速度:").pack(side="left")
        speed_scale = ttk.Scale(
            speed_frame,
            from_=1,
            to=10,
            variable=self.speed,
            orient="horizontal",
            value=2  # 设置初始值
        )
        speed_scale.pack(side="left", fill="x", expand=True, padx=5)
        
        # 透明度设置
        trans_frame = ttk.Frame(param_frame)
        trans_frame.pack(fill="x", pady=5)
        ttk.Label(trans_frame, text="透明度:  ").pack(side="left")
        trans_scale = ttk.Scale(
            trans_frame,
            from_=0.1,
            to=1.0,
            variable=self.transparent,
            orient="horizontal",
            value=0.5  # 设置初始值
        )
        trans_scale.pack(side="left", fill="x", expand=True, padx=5)
        
        # 开始按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(pady=20)
        start_btn = ttk.Button(
            btn_frame, 
            text="开始处理", 
            command=self.start_process, 
            bootstyle="success",
            width=20
        )
        start_btn.pack(pady=10)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_label = ttk.Label(
            self.root, 
            textvariable=self.status_var,
            relief="sunken",
            font=("微软雅黑", 9)
        )
        self.status_label.pack(side="bottom", fill="x", padx=10, pady=5)

    def select_video(self):
        filenames = filedialog.askopenfilenames(filetypes=[("视频文件", "*.mp4")])
        if filenames:
            self.video_path.set(";".join(filenames))  # 使用分号连接多个文件路径
            self.file_count_label.config(text=f"已选择 {len(filenames)} 个文件")

    def select_watermark(self):
        filename = filedialog.askopenfilename(filetypes=[("PNG文件", "*.png")])
        if filename:
            self.watermark_path.set(filename)

    def select_output(self):
        folder = filedialog.askdirectory()
        if folder:
            self.output_path.set(folder)

    async def add_watermark(self):
        # 获取所有视频文件路径
        video_paths = self.video_path.get().split(";")
        total_videos = len(video_paths)
        
        # 创建进度显示
        progress_frame = ttk.Frame(self.root)
        progress_frame.pack(side="bottom", fill="x", padx=10, pady=5)
        
        progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(
            progress_frame,
            variable=progress_var,
            maximum=total_videos,
            bootstyle="success-striped"
        )
        progress_bar.pack(fill="x")
        
        async def process_video(video_path, video_index):
            temp_video_path = None
            video = None
            output_video = None
            original_video = None
            watermarked_video = None
            final_video = None
            success = False
            
            try:
                # 更新状态
                self.status_var.set(f"正在处理: {os.path.basename(video_path)} ({video_index + 1}/{total_videos})")
                self.root.update()
                
                # 检查文件是否存在
                if not os.path.exists(video_path):
                    raise FileNotFoundError("视频文件不存在")
                if not os.path.exists(self.watermark_path.get()):
                    raise FileNotFoundError("水印图片不存在")
                
                # 修改临时文件路径，使用视频索引避免冲突
                temp_video_path = os.path.join(
                    self.output_path.get(), 
                    f"temp_{video_index}_{int(time.time())}.mp4"
                )
                temp_audio_path = os.path.join(
                    self.output_path.get(), 
                    f"temp_audio_{video_index}_{int(time.time())}.m4a"
                )
                
                video = cv2.VideoCapture(video_path)
                if not video.isOpened():
                    raise Exception("无法打开视频文件")
                
                # 读取水印图片并检查
                watermark = cv2.imread(self.watermark_path.get(), cv2.IMREAD_UNCHANGED)
                if watermark is None:
                    raise Exception("无法读取水印图片，请确保图片格式正确且包含透明通道")
                
                # 检查水印是否包含透明通道
                if watermark.shape[2] != 4:
                    raise Exception("水印图片必须包含透明通道(PNG格式)")
                
                # 检查并调整水印大小
                width = self.watermark_width.get()
                height = self.watermark_height.get()
                if width <= 0 or height <= 0:
                    raise ValueError("水印尺寸必须大于0")
                
                watermark = cv2.resize(watermark, (width, height))
                
                # 设置输出视频
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                fps = video.get(cv2.CAP_PROP_FPS)
                frame_width = int(video.get(cv2.CAP_PROP_FRAME_WIDTH))
                frame_height = int(video.get(cv2.CAP_PROP_FRAME_HEIGHT))
                
                # 检查输出路径
                output_dir = self.output_path.get()
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)
                
                # 修改输出路径为临时文件
                output_video = cv2.VideoWriter(
                    temp_video_path, 
                    fourcc, 
                    fps, 
                    (frame_width, frame_height), 
                    isColor=True
                )
                
                if not output_video.isOpened():
                    raise Exception("无法创建输出视频文件")
                
                # 初始化水印位置和速度
                x_offset, y_offset = 20, 20
                x_speed = y_speed = self.speed.get()
                
                frame_count = int(video.get(cv2.CAP_PROP_FRAME_COUNT))
                current_frame = 0
                
                while True:
                    ret, frame = video.read()
                    if not ret:
                        break
                    
                    current_frame += 1
                    self.status_var.set(f"处理进度: {current_frame}/{frame_count}")
                    self.root.update()
                    
                    # 更新水印位置
                    y_offset += y_speed
                    x_offset += x_speed
                    
                    # 边界检查
                    if y_offset + watermark.shape[0] > frame_height or y_offset < 0:
                        y_speed *= -1
                        y_offset = max(0, min(y_offset, frame_height - watermark.shape[0]))
                    if x_offset + watermark.shape[1] > frame_width or x_offset < 0:
                        x_speed *= -1
                        x_offset = max(0, min(x_offset, frame_width - watermark.shape[1]))
                    
                    # 添加水印
                    y1, y2 = int(y_offset), int(y_offset + watermark.shape[0])
                    x1, x2 = int(x_offset), int(x_offset + watermark.shape[1])
                    
                    # 确保坐标在有效范围内
                    if (y1 >= 0 and y2 <= frame_height and 
                        x1 >= 0 and x2 <= frame_width):
                        
                        alpha_s = (watermark[:, :, 3] / 255.0) * self.transparent.get()
                        alpha_l = 1.0 - alpha_s
                        
                        for c in range(0, 3):
                            frame[y1:y2, x1:x2, c] = (
                                alpha_s * watermark[:, :, c] +
                                alpha_l * frame[y1:y2, x1:x2, c]
                            )
                    
                    output_video.write(frame)
                
                # 释放OpenCV资源
                if video is not None:
                    video.release()
                if output_video is not None:
                    output_video.release()
                cv2.destroyAllWindows()
                
                # 使用moviepy合并视频和音频
                self.status_var.set("正在合并音频...")
                self.root.update()
                
                try:
                    # 获取程序运行目录
                    if getattr(sys, 'frozen', False):
                        # 如果是打包后的程序
                        application_path = os.path.dirname(sys.executable)
                    else:
                        # 如果是开发环境
                        application_path = os.path.dirname(os.path.abspath(__file__))
                        
                    # 设置 ImageMagick 路径
                    imagemagick_path = os.path.join(application_path, 'ImageMagick-7.0.10-Q16', 'magick.exe')
                    if os.path.exists(imagemagick_path):
                        # 设置 ImageMagick 的可执行文件路径,用于 moviepy 处理视频时调用 ImageMagick 进行图像处理
                        change_settings({"IMAGEMAGICK_BINARY": imagemagick_path})
                    
                    # 读取原始视频的音频
                    original_video = VideoFileClip(video_path)
                    if original_video.audio is None:
                        # 如果原视频没有音频，直接重命名临时文件为最终文件
                        if os.path.exists(temp_video_path):
                            final_output_path = os.path.join(self.output_path.get(), "watermarked_" + os.path.basename(video_path))
                            if os.path.exists(final_output_path):
                                os.remove(final_output_path)
                            os.rename(temp_video_path, final_output_path)
                            return True
                    
                    # 读取添加了水印的视频
                    watermarked_video = VideoFileClip(temp_video_path)
                    if watermarked_video is None:
                        raise Exception("无法读取处理后的视频文件")
                        
                    # 设置音频
                    final_video = watermarked_video.set_audio(original_video.audio)
                    if final_video is None:
                        raise Exception("合并音频失败")
                        
                    # 保存最终视频
                    final_output_path = os.path.join(self.output_path.get(), "watermarked_" + os.path.basename(video_path))
                    if os.path.exists(final_output_path):
                        os.remove(final_output_path)
                        
                    final_video.write_videofile(
                        final_output_path,
                        codec='libx264',
                        audio_codec='aac',
                        temp_audiofile=temp_audio_path,  # 使用唯一的临时音频文件路径
                        remove_temp=True,
                        verbose=False,
                        logger=None
                    )
                    
                except Exception as e:
                    raise Exception(f"音频处理失败: {str(e)}")
                
                finally:
                    # 确保关闭所有moviepy的视频对象
                    try:
                        if original_video is not None:
                            original_video.close()
                        if watermarked_video is not None:
                            watermarked_video.close()
                        if final_video is not None:
                            final_video.close()
                    except Exception as e:
                        print(f"关闭视频对象时出错: {e}")
                    
                    # 等待一段时间确保文件句柄被释放
                    time.sleep(1)
                    
                    # 删除临时文件
                    for temp_file in [temp_video_path, temp_audio_path]:
                        if temp_file and os.path.exists(temp_file):
                            try:
                                os.remove(temp_file)
                            except Exception as e:
                                print(f"删除临时文件失败: {e}")

                # 在成功完成所有处理后设置成功标志
                success = True
                
            except Exception as e:
                # 记录错误但不显示消息框（避免多次弹窗）
                print(f"处理视频 {os.path.basename(video_path)} 时出错：{str(e)}")
                self.status_var.set(f"处理失败: {os.path.basename(video_path)}")
            finally:
                # 清理资源
                if video is not None:
                    video.release()
                if output_video is not None:
                    output_video.release()
                cv2.destroyAllWindows()
                
                # 关闭 moviepy 对象
                for clip in [original_video, watermarked_video, final_video]:
                    if clip is not None:
                        try:
                            clip.close()
                        except:
                            pass
                
                # 删除临时文件
                for temp_file in [temp_video_path, temp_audio_path]:
                    if temp_file and os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except:
                            pass
                
                # 更新进度
                progress_var.set(video_index + 1)
                self.root.update()
            return success  # 返回处理结果

        try:
            # 创建所有任务
            tasks = [process_video(path, idx) for idx, path in enumerate(video_paths)]
            
            # 并发执行所有任务
            results = await asyncio.gather(*tasks)
            
            # 统计结果
            success_count = sum(1 for r in results if r)
            failed_count = len(results) - success_count
            
            # 显示结果
            if failed_count == 0:
                self.status_var.set("所有视频处理完成！")
                messagebox.showinfo("完成", f"已成功处理 {success_count} 个视频！")
            else:
                self.status_var.set(f"处理完成 (成功: {success_count}, 失败: {failed_count})")
                messagebox.showwarning("完成", 
                    f"处理完成\n成功: {success_count} 个\n失败: {failed_count} 个")
        
        except Exception as e:
            messagebox.showerror("错误", f"批量处理过程中出现错误：{str(e)}")
            self.status_var.set("批量处理失败")
        finally:
            progress_frame.destroy()

    def start_process(self):
        if not self.video_path.get() or not self.watermark_path.get() or not self.output_path.get():
            messagebox.showwarning("警告", "请选择所有必要的文件和路径")
            return
        
        # 启动异步处理
        async def run_async():
            await self.add_watermark()
        
        def start_async():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(run_async())
            finally:
                loop.close()
        
        Thread(target=start_async, daemon=True).start()

    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    app = VideoWatermark()
    app.run() 