# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['F:\\soft\\python\\workdata\\vidieo_mark\\main.py'],
    pathex=[],
    binaries=[],
    datas=[('F:\\soft\\ffmpeg\\bin\\ffmpeg.exe', 'ffmpeg')],
    hiddenimports=['moviepy', 'moviepy.editor', 'imageio', 'imageio_ffmpeg', 'torch.utils.tensorboard', 'tensorboard', 'cv2'],
    hookspath=['.'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='main',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['F:\\soft\\python\\crm_data\\111.ico'],
)
